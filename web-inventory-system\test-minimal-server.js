// Minimal Test Server
const express = require('express');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const app = express();
const PORT = 3000;

console.log('🚀 Starting minimal test server...');

// Database connection
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

app.use(express.json());
app.use(express.static('.'));

// Test route
app.get('/api/test', async (req, res) => {
    try {
        const result = await pool.query('SELECT COUNT(*) as count FROM products');
        res.json({ 
            success: true, 
            message: 'Database connected!', 
            productCount: result.rows[0].count 
        });
    } catch (error) {
        res.status(500).json({ 
            success: false, 
            error: error.message 
        });
    }
});

// Products API
app.get('/api/products', async (req, res) => {
    try {
        const query = `
            SELECT 
                p.id,
                p.product_code as "hsnCode",
                p.name,
                c.name as category,
                p.cost_price as "costPrice",
                p.selling_price as "sellingPrice",
                v.name as supplier,
                p.reorder_level as "reorderLevel",
                p.max_stock_level as "maxStockLevel",
                p.current_stock as "currentStock"
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN vendors v ON p.vendor_id = v.id
            WHERE p.is_active = TRUE
            ORDER BY p.name
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Products API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server
async function startServer() {
    try {
        // Test database connection first
        console.log('📡 Testing database connection...');
        await pool.query('SELECT 1');
        console.log('✅ Database connection successful');
        
        app.listen(PORT, () => {
            console.log(`🚀 Test server running on http://localhost:${PORT}`);
            console.log(`📊 Test API: http://localhost:${PORT}/api/test`);
            console.log(`📦 Products API: http://localhost:${PORT}/api/products`);
            console.log(`🌐 Web App: http://localhost:${PORT}`);
        });
    } catch (error) {
        console.error('❌ Server startup failed:', error.message);
        process.exit(1);
    }
}

startServer();

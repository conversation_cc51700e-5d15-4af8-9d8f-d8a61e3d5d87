<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test All 14 Tables - CRUD Operations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .table-section { margin-bottom: 2rem; }
        .api-status { padding: 0.25rem 0.5rem; margin: 0.25rem; border-radius: 0.25rem; }
        .status-success { background-color: #d4edda; color: #155724; }
        .status-error { background-color: #f8d7da; color: #721c24; }
        .test-results { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1 class="mb-4">🧪 Complete 14-Table CRUD Test Suite</h1>
        
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5>🔧 Test Controls</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2 w-100" onclick="testAllAPIs()">Test All 14 APIs</button>
                        <button class="btn btn-success mb-2 w-100" onclick="addSampleData()">Add Sample Data</button>
                        <button class="btn btn-info mb-2 w-100" onclick="testCRUDOperations()">Test CRUD Operations</button>
                        <button class="btn btn-warning mb-2 w-100" onclick="clearResults()">Clear Results</button>
                        
                        <hr>
                        <h6>Quick Add Forms</h6>
                        
                        <div class="mb-3">
                            <label class="form-label">Vendor Name</label>
                            <input type="text" class="form-control" id="vendorName" placeholder="Test Vendor">
                            <button class="btn btn-sm btn-outline-primary mt-1" onclick="quickAddVendor()">Add Vendor</button>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Customer Name</label>
                            <input type="text" class="form-control" id="customerName" placeholder="Test Customer">
                            <button class="btn btn-sm btn-outline-primary mt-1" onclick="quickAddCustomer()">Add Customer</button>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Part Description</label>
                            <input type="text" class="form-control" id="partDesc" placeholder="Test Part">
                            <button class="btn btn-sm btn-outline-primary mt-1" onclick="quickAddPart()">Add Part</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5>📊 Test Results</h5>
                    </div>
                    <div class="card-body test-results" id="testResults">
                        <p class="text-muted">Click "Test All 14 APIs" to start testing...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>📋 All 14 Tables Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="tableStatus" class="d-flex flex-wrap">
                            <!-- Status badges will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>🔍 Data Viewer</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <select class="form-select" id="tableSelector" onchange="loadTableData()">
                                <option value="">Select a table to view data</option>
                                <option value="vendors">Vendors</option>
                                <option value="customers">Customers</option>
                                <option value="parts">Parts</option>
                                <option value="units">Units</option>
                                <option value="users">Users</option>
                                <option value="user-roles">User Roles</option>
                                <option value="taxes">Taxes</option>
                                <option value="indents">Indent Requests</option>
                                <option value="purchase-orders">Purchase Orders</option>
                                <option value="goods-receipts">Goods Receipts</option>
                                <option value="sales-invoices">Sales Invoices</option>
                                <option value="accounts-payable">Accounts Payable</option>
                                <option value="accounts-receivable">Accounts Receivable</option>
                                <option value="stock-updates">Stock Updates</option>
                            </select>
                        </div>
                        <div id="tableData" class="table-responsive">
                            <!-- Table data will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';
        
        const tableEndpoints = [
            'vendors', 'customers', 'parts', 'units', 'users', 'user-roles', 'taxes',
            'indents', 'purchase-orders', 'goods-receipts', 'sales-invoices',
            'accounts-payable', 'accounts-receivable', 'stock-updates'
        ];

        // Test all 14 APIs
        async function testAllAPIs() {
            const results = document.getElementById('testResults');
            const status = document.getElementById('tableStatus');
            
            results.innerHTML = '<h6>🧪 Testing All 14 Table APIs...</h6>';
            status.innerHTML = '';
            
            let totalRecords = 0;
            let workingAPIs = 0;
            
            for (const endpoint of tableEndpoints) {
                try {
                    const response = await fetch(`${API_BASE}/${endpoint}`);
                    const result = await response.json();
                    
                    if (result.success) {
                        const count = result.data.length;
                        totalRecords += count;
                        workingAPIs++;
                        
                        results.innerHTML += `<div class="api-status status-success">✅ ${endpoint}: ${count} records</div>`;
                        status.innerHTML += `<span class="badge bg-success me-2 mb-2">${endpoint}: ${count}</span>`;
                    } else {
                        results.innerHTML += `<div class="api-status status-error">❌ ${endpoint}: ${result.error}</div>`;
                        status.innerHTML += `<span class="badge bg-danger me-2 mb-2">${endpoint}: Error</span>`;
                    }
                } catch (error) {
                    results.innerHTML += `<div class="api-status status-error">❌ ${endpoint}: ${error.message}</div>`;
                    status.innerHTML += `<span class="badge bg-danger me-2 mb-2">${endpoint}: Failed</span>`;
                }
            }
            
            results.innerHTML += `<hr><h6>📊 Summary: ${workingAPIs}/14 APIs working, ${totalRecords} total records</h6>`;
        }

        // Add sample data to all tables
        async function addSampleData() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h6>📝 Adding Sample Data...</h6>';
            
            // Sample data for each table
            const sampleData = {
                vendors: { company: 1, vendor_code: Date.now(), vendor_name: 'Sample Vendor', city: 'Mumbai', active: true, created_by: 'TEST' },
                customers: { company: 1, customer_code: Date.now(), customer_name: 'Sample Customer', city: 'Delhi', active: true, created_by: 'TEST' },
                parts: { company: 1, part_num: Date.now(), part_desc: 'Sample Part', pur_price: 100, sell_price: 120, active: true, created_by: 'TEST' },
                units: { company: 1, unit_id: Date.now() % 1000, unit_desc: 'Sample Unit', active: true, created_by: 'TEST' },
                users: { company: 1, user_id: 'TEST' + Date.now(), user_key: 'test123', active: true, created_by: 'TEST' },
                'user-roles': { company: 1, role_id: Date.now() % 1000, role_desc: 'Sample Role', active: true, created_by: 'TEST' },
                taxes: { company: 1, tax_id: Date.now() % 1000, tax_desc: 'Sample Tax', tax_percent: 18, active: true, created_by: 'TEST' }
            };
            
            for (const [endpoint, data] of Object.entries(sampleData)) {
                try {
                    const response = await fetch(`${API_BASE}/${endpoint}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(data)
                    });
                    const result = await response.json();
                    
                    if (result.success) {
                        results.innerHTML += `<div class="api-status status-success">✅ Added to ${endpoint}</div>`;
                    } else {
                        results.innerHTML += `<div class="api-status status-error">❌ Failed to add to ${endpoint}: ${result.error}</div>`;
                    }
                } catch (error) {
                    results.innerHTML += `<div class="api-status status-error">❌ Error adding to ${endpoint}: ${error.message}</div>`;
                }
            }
            
            results.innerHTML += '<hr><p>✅ Sample data addition completed. Run "Test All 14 APIs" to see updated counts.</p>';
        }

        // Test CRUD operations
        async function testCRUDOperations() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<h6>🔄 Testing CRUD Operations...</h6>';
            
            // Test CREATE, READ, UPDATE, DELETE on vendors table
            const testData = {
                company: 1,
                vendor_code: 99999,
                vendor_name: 'CRUD Test Vendor',
                city: 'Test City',
                active: true,
                created_by: 'CRUD_TEST'
            };
            
            try {
                // CREATE
                const createResponse = await fetch(`${API_BASE}/vendors`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const createResult = await createResponse.json();
                
                if (createResult.success) {
                    results.innerHTML += '<div class="api-status status-success">✅ CREATE: Successfully added test vendor</div>';
                    
                    // READ
                    const readResponse = await fetch(`${API_BASE}/vendors`);
                    const readResult = await readResponse.json();
                    const foundVendor = readResult.data.find(v => v.vendor_code == 99999);
                    
                    if (foundVendor) {
                        results.innerHTML += '<div class="api-status status-success">✅ READ: Successfully found test vendor</div>';
                        
                        // UPDATE
                        const updateData = { ...testData, vendor_name: 'UPDATED Test Vendor' };
                        const updateResponse = await fetch(`${API_BASE}/vendors/1/99999`, {
                            method: 'PUT',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(updateData)
                        });
                        
                        if (updateResponse.ok) {
                            results.innerHTML += '<div class="api-status status-success">✅ UPDATE: Successfully updated test vendor</div>';
                        } else {
                            results.innerHTML += '<div class="api-status status-error">❌ UPDATE: Failed to update test vendor</div>';
                        }
                        
                        // DELETE
                        const deleteResponse = await fetch(`${API_BASE}/vendors/1/99999`, {
                            method: 'DELETE'
                        });
                        
                        if (deleteResponse.ok) {
                            results.innerHTML += '<div class="api-status status-success">✅ DELETE: Successfully deleted test vendor</div>';
                        } else {
                            results.innerHTML += '<div class="api-status status-error">❌ DELETE: Failed to delete test vendor</div>';
                        }
                    } else {
                        results.innerHTML += '<div class="api-status status-error">❌ READ: Could not find test vendor</div>';
                    }
                } else {
                    results.innerHTML += `<div class="api-status status-error">❌ CREATE: ${createResult.error}</div>`;
                }
            } catch (error) {
                results.innerHTML += `<div class="api-status status-error">❌ CRUD Test Error: ${error.message}</div>`;
            }
            
            results.innerHTML += '<hr><p>✅ CRUD operations test completed.</p>';
        }

        // Quick add functions
        async function quickAddVendor() {
            const name = document.getElementById('vendorName').value || 'Quick Vendor';
            const data = {
                company: 1,
                vendor_code: Date.now(),
                vendor_name: name,
                city: 'Mumbai',
                active: true,
                created_by: 'QUICK_ADD'
            };
            
            try {
                const response = await fetch(`${API_BASE}/vendors`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Vendor added successfully!');
                    document.getElementById('vendorName').value = '';
                } else {
                    alert('❌ Error: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error: ' + error.message);
            }
        }

        async function quickAddCustomer() {
            const name = document.getElementById('customerName').value || 'Quick Customer';
            const data = {
                company: 1,
                customer_code: Date.now(),
                customer_name: name,
                city: 'Delhi',
                active: true,
                created_by: 'QUICK_ADD'
            };
            
            try {
                const response = await fetch(`${API_BASE}/customers`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Customer added successfully!');
                    document.getElementById('customerName').value = '';
                } else {
                    alert('❌ Error: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error: ' + error.message);
            }
        }

        async function quickAddPart() {
            const desc = document.getElementById('partDesc').value || 'Quick Part';
            const data = {
                company: 1,
                part_num: Date.now(),
                part_desc: desc,
                pur_price: 100,
                sell_price: 120,
                active: true,
                created_by: 'QUICK_ADD'
            };
            
            try {
                const response = await fetch(`${API_BASE}/parts`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Part added successfully!');
                    document.getElementById('partDesc').value = '';
                } else {
                    alert('❌ Error: ' + result.error);
                }
            } catch (error) {
                alert('❌ Error: ' + error.message);
            }
        }

        // Load table data
        async function loadTableData() {
            const selector = document.getElementById('tableSelector');
            const dataDiv = document.getElementById('tableData');
            const endpoint = selector.value;
            
            if (!endpoint) {
                dataDiv.innerHTML = '';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/${endpoint}`);
                const result = await response.json();
                
                if (result.success && result.data.length > 0) {
                    const data = result.data;
                    const headers = Object.keys(data[0]);
                    
                    let tableHtml = `
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>${headers.map(h => `<th>${h}</th>`).join('')}</tr>
                            </thead>
                            <tbody>
                                ${data.map(row => `
                                    <tr>${headers.map(h => `<td>${row[h] || '-'}</td>`).join('')}</tr>
                                `).join('')}
                            </tbody>
                        </table>
                    `;
                    
                    dataDiv.innerHTML = tableHtml;
                } else {
                    dataDiv.innerHTML = `<p class="text-muted">No data found in ${endpoint} table.</p>`;
                }
            } catch (error) {
                dataDiv.innerHTML = `<p class="text-danger">Error loading ${endpoint}: ${error.message}</p>`;
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '<p class="text-muted">Results cleared. Ready for new tests.</p>';
            document.getElementById('tableStatus').innerHTML = '';
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(testAllAPIs, 1000);
        });
    </script>
</body>
</html>

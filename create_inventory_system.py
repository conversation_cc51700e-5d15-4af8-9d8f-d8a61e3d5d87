import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import random

def create_inventory_management_system():
    """
    Create a comprehensive 9-sheet inventory management system
    """
    print("🚀 Creating Inventory Management System...")
    
    # Create Excel writer object
    filename = "Advanced_Inventory_Management_System.xlsx"
    writer = pd.ExcelWriter(filename, engine='openpyxl')
    
    # 1. PRODUCTS SHEET
    print("📦 Creating Products sheet...")
    products_data = {
        'HSN Code': ['P1001', 'P1002', 'P1003', 'P1004', 'P1005', 'P1006', 'P1007', 'P1008', 'P1009', 'P1010', 'P1011', 'P1012'],
        'Product Name': ['Gaming Laptop', 'Wireless Mouse', 'Mechanical Keyboard', '4K Monitor', 'Webcam HD', 'Bluetooth Speaker', 
                        'External SSD 1TB', 'Graphics Card', 'Processor Intel i7', 'RAM 16GB DDR4', 'Power Supply 750W', 'Cooling Fan'],
        'Category': ['Electronics', 'Accessories', 'Accessories', 'Electronics', 'Electronics', 'Electronics',
                    'Storage', 'Components', 'Components', 'Components', 'Components', 'Components'],
        'Cost Price': [45000, 1200, 3500, 25000, 4500, 2800, 8500, 35000, 28000, 6500, 4200, 1800],
        'Selling Price': [52000, 1440, 4200, 30000, 5400, 3360, 10200, 42000, 33600, 7800, 5040, 2160],
        'Supplier': ['TechCorp', 'AccessoryHub', 'AccessoryHub', 'DisplayTech', 'TechCorp', 'AudioMax',
                     'StoragePro', 'ComponentKing', 'ComponentKing', 'ComponentKing', 'ComponentKing', 'ComponentKing'],
        'Reorder Level': [5, 20, 15, 8, 12, 10, 15, 6, 8, 12, 10, 25],
        'Max Stock Level': [50, 100, 80, 40, 60, 50, 75, 30, 40, 60, 50, 100]
    }
    products_df = pd.DataFrame(products_data)
    products_df.to_excel(writer, sheet_name='Products', index=False)
    
    # 2. VENDORS SHEET
    print("🏢 Creating Vendors sheet...")
    vendors_data = {
        'Vendor ID': ['V001', 'V002', 'V003', 'V004', 'V005'],
        'Vendor Name': ['TechCorp', 'AccessoryHub', 'DisplayTech', 'AudioMax', 'StoragePro'],
        'Contact Person': ['Rajesh Kumar', 'Priya Sharma', 'Amit Singh', 'Neha Gupta', 'Vikram Patel'],
        'Phone': ['9876543210', '9876543211', '9876543212', '9876543213', '9876543214'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
        'Address': ['Mumbai, Maharashtra', 'Delhi, NCR', 'Bangalore, Karnataka', 'Pune, Maharashtra', 'Chennai, Tamil Nadu'],
        'Payment Terms': ['30 Days', '15 Days', '45 Days', '30 Days', '30 Days'],
        'Rating': [4.5, 4.2, 4.7, 4.3, 4.6]
    }
    vendors_df = pd.DataFrame(vendors_data)
    vendors_df.to_excel(writer, sheet_name='Vendors', index=False)
    
    # 3. CUSTOMERS SHEET
    print("👥 Creating Customers sheet...")
    customers_data = {
        'Customer ID': ['C001', 'C002', 'C003', 'C004', 'C005', 'C006', 'C007', 'C008', 'C009', 'C010'],
        'Customer Name': ['Tech Solutions Ltd', 'Gaming Zone', 'Office Supplies Co', 'Digital World', 'Smart Systems',
                         'Cyber Cafe Network', 'Educational Institute', 'Startup Hub', 'Corporate Solutions', 'Retail Electronics'],
        'Contact Person': ['Suresh Reddy', 'Anita Joshi', 'Rohit Mehta', 'Kavya Nair', 'Deepak Agarwal',
                          'Sanjay Kumar', 'Dr. Meera Shah', 'Arjun Kapoor', 'Ravi Gupta', 'Pooja Singh'],
        'Phone': ['9123456789', '9123456790', '9123456791', '9123456792', '9123456793',
                 '9123456794', '9123456795', '9123456796', '9123456797', '9123456798'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
                 '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
                 '<EMAIL>', '<EMAIL>'],
        'Address': ['Hyderabad, Telangana', 'Mumbai, Maharashtra', 'Delhi, NCR', 'Kochi, Kerala', 'Jaipur, Rajasthan',
                   'Kolkata, West Bengal', 'Pune, Maharashtra', 'Bangalore, Karnataka', 'Chennai, Tamil Nadu', 'Ahmedabad, Gujarat'],
        'Customer Type': ['B2B', 'B2C', 'B2B', 'B2C', 'B2B', 'B2B', 'B2B', 'B2B', 'B2B', 'B2C'],
        'Credit Limit': [500000, 100000, 300000, 150000, 400000, 250000, 600000, 350000, 450000, 200000]
    }
    customers_df = pd.DataFrame(customers_data)
    customers_df.to_excel(writer, sheet_name='Customers', index=False)
    
    # 4. PURCHASE SHEET
    print("🛒 Creating Purchase sheet...")
    # Generate purchase data for the last 30 days
    purchase_records = []
    base_date = datetime.now() - timedelta(days=30)
    
    for i in range(25):  # 25 purchase records
        record_date = base_date + timedelta(days=random.randint(0, 30))
        product_idx = random.randint(0, len(products_data['HSN Code'])-1)
        vendor_name = products_data['Supplier'][product_idx]
        quantity = random.randint(10, 100)
        cost_price = products_data['Cost Price'][product_idx]
        
        purchase_records.append({
            'Purchase ID': f'PUR{1001+i}',
            'Date': record_date.strftime('%Y-%m-%d'),
            'HSN Code': products_data['HSN Code'][product_idx],
            'Product Name': products_data['Product Name'][product_idx],
            'Vendor': vendor_name,
            'Quantity': quantity,
            'Unit Cost': cost_price,
            'Total Amount': quantity * cost_price,
            'Status': random.choice(['Received', 'Pending', 'Partial'])
        })
    
    purchase_df = pd.DataFrame(purchase_records)
    purchase_df.to_excel(writer, sheet_name='Purchase', index=False)
    
    # 5. SALES SHEET
    print("💰 Creating Sales sheet...")
    # Generate sales data
    sales_records = []
    
    for i in range(30):  # 30 sales records
        record_date = base_date + timedelta(days=random.randint(0, 30))
        product_idx = random.randint(0, len(products_data['HSN Code'])-1)
        customer_idx = random.randint(0, len(customers_data['Customer ID'])-1)
        quantity = random.randint(1, 20)
        selling_price = products_data['Selling Price'][product_idx]
        
        sales_records.append({
            'Sale ID': f'SAL{2001+i}',
            'Date': record_date.strftime('%Y-%m-%d'),
            'Customer ID': customers_data['Customer ID'][customer_idx],
            'Customer Name': customers_data['Customer Name'][customer_idx],
            'HSN Code': products_data['HSN Code'][product_idx],
            'Product Name': products_data['Product Name'][product_idx],
            'Quantity': quantity,
            'Unit Price': selling_price,
            'Total Amount': quantity * selling_price,
            'Payment Status': random.choice(['Paid', 'Pending', 'Partial']),
            'Payment Method': random.choice(['Cash', 'Card', 'UPI', 'Bank Transfer'])
        })
    
    sales_df = pd.DataFrame(sales_records)
    sales_df.to_excel(writer, sheet_name='Sales', index=False)
    
    # 6. INVENTORY SHEET
    print("📊 Creating Inventory sheet...")
    # Calculate current stock levels
    inventory_records = []
    
    for i, product in enumerate(products_data['HSN Code']):
        # Calculate purchased quantity
        purchased = purchase_df[purchase_df['HSN Code'] == product]['Quantity'].sum()
        # Calculate sold quantity
        sold = sales_df[sales_df['HSN Code'] == product]['Quantity'].sum()
        # Current stock
        current_stock = max(0, purchased - sold)
        
        # Stock status
        reorder_level = products_data['Reorder Level'][i]
        if current_stock <= reorder_level:
            status = 'CRITICAL - REORDER NOW'
        elif current_stock <= reorder_level * 1.5:
            status = 'LOW STOCK'
        else:
            status = 'ADEQUATE'
        
        inventory_records.append({
            'HSN Code': product,
            'Product Name': products_data['Product Name'][i],
            'Category': products_data['Category'][i],
            'Current Stock': current_stock,
            'Reorder Level': reorder_level,
            'Max Stock Level': products_data['Max Stock Level'][i],
            'Unit Cost': products_data['Cost Price'][i],
            'Stock Value': current_stock * products_data['Cost Price'][i],
            'Status': status,
            'Supplier': products_data['Supplier'][i],
            'Last Updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
    
    inventory_df = pd.DataFrame(inventory_records)
    inventory_df.to_excel(writer, sheet_name='Inventory', index=False)
    
    return writer, products_df, vendors_df, customers_df, purchase_df, sales_df, inventory_df

def create_remaining_sheets(writer, products_df, vendors_df, customers_df, purchase_df, sales_df, inventory_df):
    """
    Create the remaining sheets: Dashboard, Analytics, and New Entry
    """
    
    # 7. DASHBOARD SHEET
    print("📈 Creating Dashboard sheet...")
    
    # Calculate key metrics
    total_products = len(products_df)
    total_customers = len(customers_df)
    total_vendors = len(vendors_df)
    total_sales_value = sales_df['Total Amount'].sum()
    total_purchase_value = purchase_df['Total Amount'].sum()
    total_inventory_value = inventory_df['Stock Value'].sum()
    
    # Critical stock items
    critical_items = inventory_df[inventory_df['Status'] == 'CRITICAL - REORDER NOW']
    low_stock_items = inventory_df[inventory_df['Status'] == 'LOW STOCK']
    
    # Create dashboard data
    dashboard_data = {
        'Metric': ['Total Products', 'Total Customers', 'Total Vendors', 'Total Sales Value', 
                  'Total Purchase Value', 'Total Inventory Value', 'Critical Stock Items', 'Low Stock Items'],
        'Value': [total_products, total_customers, total_vendors, f'₹{total_sales_value:,.2f}',
                 f'₹{total_purchase_value:,.2f}', f'₹{total_inventory_value:,.2f}', 
                 len(critical_items), len(low_stock_items)],
        'Status': ['✅', '✅', '✅', '✅', '✅', '✅', 
                  '🚨' if len(critical_items) > 0 else '✅',
                  '⚠️' if len(low_stock_items) > 0 else '✅']
    }
    
    dashboard_df = pd.DataFrame(dashboard_data)
    
    # Add alerts section
    alerts_data = []
    alerts_data.append(['ALERTS & NOTIFICATIONS', '', ''])
    
    for _, item in critical_items.iterrows():
        alerts_data.append([f"🚨 CRITICAL: {item['Product Name']}", 
                           f"Stock: {item['Current Stock']} units", 
                           f"Contact: {item['Supplier']}"])
    
    for _, item in low_stock_items.iterrows():
        alerts_data.append([f"⚠️ LOW STOCK: {item['Product Name']}", 
                           f"Stock: {item['Current Stock']} units", 
                           f"Reorder Level: {item['Reorder Level']}"])
    
    # Combine dashboard and alerts
    combined_data = []
    for i, row in dashboard_df.iterrows():
        combined_data.append([row['Metric'], row['Value'], row['Status']])
    
    combined_data.append(['', '', ''])  # Empty row
    combined_data.extend(alerts_data)
    
    final_dashboard_df = pd.DataFrame(combined_data, columns=['Metric', 'Value', 'Status'])
    final_dashboard_df.to_excel(writer, sheet_name='Dashboard', index=False)
    
    # 8. ANALYTICS SHEET
    print("📊 Creating Analytics sheet...")
    
    # Sales by customer
    sales_by_customer = sales_df.groupby('Customer Name')['Total Amount'].sum().reset_index()
    sales_by_customer = sales_by_customer.sort_values('Total Amount', ascending=False).head(10)
    
    # Sales by product
    sales_by_product = sales_df.groupby('Product Name')['Quantity'].sum().reset_index()
    sales_by_product = sales_by_product.sort_values('Quantity', ascending=False).head(10)
    
    # Purchase by vendor
    purchase_by_vendor = purchase_df.groupby('Vendor')['Total Amount'].sum().reset_index()
    purchase_by_vendor = purchase_by_vendor.sort_values('Total Amount', ascending=False)
    
    # Monthly sales trend
    sales_df['Date'] = pd.to_datetime(sales_df['Date'])
    monthly_sales = sales_df.groupby(sales_df['Date'].dt.strftime('%Y-%m'))['Total Amount'].sum().reset_index()
    
    # Create analytics summary
    analytics_data = []
    analytics_data.append(['SALES ANALYTICS', '', ''])
    analytics_data.append(['Top Customers by Revenue', '', ''])
    for _, row in sales_by_customer.iterrows():
        analytics_data.append([row['Customer Name'], f"₹{row['Total Amount']:,.2f}", ''])
    
    analytics_data.append(['', '', ''])
    analytics_data.append(['Top Products by Quantity Sold', '', ''])
    for _, row in sales_by_product.iterrows():
        analytics_data.append([row['Product Name'], f"{row['Quantity']} units", ''])
    
    analytics_data.append(['', '', ''])
    analytics_data.append(['Purchase by Vendor', '', ''])
    for _, row in purchase_by_vendor.iterrows():
        analytics_data.append([row['Vendor'], f"₹{row['Total Amount']:,.2f}", ''])
    
    analytics_df = pd.DataFrame(analytics_data, columns=['Category', 'Value', 'Additional Info'])
    analytics_df.to_excel(writer, sheet_name='Analytics', index=False)
    
    # 9. NEW ENTRY SHEET
    print("➕ Creating New Entry sheet...")
    
    new_entry_data = {
        'Entry Type': ['New Product Entry', 'New Customer Entry', 'New Vendor Entry', 'Purchase Entry', 'Sales Entry'],
        'Description': [
            'Add new products to inventory system',
            'Register new customers',
            'Add new suppliers/vendors',
            'Record new purchase transactions',
            'Record new sales transactions'
        ],
        'Required Fields': [
            'HSN Code, Product Name, Category, Cost Price, Selling Price, Supplier',
            'Customer ID, Name, Contact, Email, Address, Type, Credit Limit',
            'Vendor ID, Name, Contact, Email, Address, Payment Terms',
            'Purchase ID, Date, Product, Vendor, Quantity, Cost',
            'Sale ID, Date, Customer, Product, Quantity, Price'
        ],
        'Status': ['Template Ready', 'Template Ready', 'Template Ready', 'Template Ready', 'Template Ready']
    }
    
    new_entry_df = pd.DataFrame(new_entry_data)
    new_entry_df.to_excel(writer, sheet_name='New Entry', index=False)
    
    print("✅ All sheets created successfully!")
    return writer

if __name__ == "__main__":
    # Create the inventory management system
    writer, products_df, vendors_df, customers_df, purchase_df, sales_df, inventory_df = create_inventory_management_system()
    
    # Create remaining sheets
    writer = create_remaining_sheets(writer, products_df, vendors_df, customers_df, purchase_df, sales_df, inventory_df)
    
    # Save the Excel file
    writer.close()
    
    print(f"\n🎉 SUCCESS! Inventory Management System created!")
    print(f"📁 File: Advanced_Inventory_Management_System.xlsx")
    print(f"📊 Sheets: 9 comprehensive worksheets")
    print(f"📦 Products: {len(products_df)} items")
    print(f"👥 Customers: {len(customers_df)} clients")
    print(f"🏢 Vendors: {len(vendors_df)} suppliers")
    print(f"🛒 Purchase Records: {len(purchase_df)} transactions")
    print(f"💰 Sales Records: {len(sales_df)} transactions")
    print(f"\n🚀 Your inventory management system is ready to use!")

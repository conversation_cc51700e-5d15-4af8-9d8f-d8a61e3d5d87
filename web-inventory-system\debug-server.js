// Debug Server - Step by step startup
const express = require('express');
const cors = require('cors');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

console.log('🔍 DEBUG: Starting server debug...');

const app = express();
const PORT = process.env.PORT || 3000;

console.log('✅ DEBUG: Express app created');

// Middleware
console.log('🔧 DEBUG: Setting up middleware...');
app.use(cors());
app.use(express.json());
app.use(express.static('.'));
console.log('✅ DEBUG: Middleware configured');

// Database configuration
console.log('🗄️ DEBUG: Setting up database connection...');
console.log(`   Host: ${process.env.DB_HOST || 'localhost'}`);
console.log(`   Port: ${process.env.DB_PORT || 5432}`);
console.log(`   Database: ${process.env.DB_NAME || 'inventory_management'}`);
console.log(`   User: ${process.env.DB_USER || 'postgres'}`);

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});

console.log('✅ DEBUG: Database pool created');

// Database event handlers
pool.on('connect', () => {
    console.log('✅ DEBUG: Database connected successfully');
});

pool.on('error', (err) => {
    console.error('❌ DEBUG: Database connection error:', err.message);
});

console.log('🔧 DEBUG: Setting up routes...');

// Simple health check
app.get('/api/health', async (req, res) => {
    console.log('🩺 DEBUG: Health check requested');
    try {
        const result = await pool.query('SELECT NOW() as time, COUNT(*) as products FROM products');
        console.log('✅ DEBUG: Health check successful');
        res.json({ 
            status: 'ok', 
            timestamp: result.rows[0].time,
            productCount: result.rows[0].products,
            database: 'connected'
        });
    } catch (error) {
        console.error('❌ DEBUG: Health check failed:', error.message);
        res.status(500).json({ 
            status: 'error', 
            error: error.message,
            database: 'disconnected'
        });
    }
});

// Products API
app.get('/api/products', async (req, res) => {
    console.log('📦 DEBUG: Products API requested');
    try {
        const query = `
            SELECT 
                p.id,
                p.product_code as "hsnCode",
                p.name,
                c.name as category,
                p.cost_price as "costPrice",
                p.selling_price as "sellingPrice",
                v.name as supplier,
                p.current_stock as "currentStock"
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN vendors v ON p.vendor_id = v.id
            WHERE p.is_active = TRUE
            ORDER BY p.name
        `;
        const result = await pool.query(query);
        console.log(`✅ DEBUG: Products API successful - ${result.rows.length} products`);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('❌ DEBUG: Products API failed:', error.message);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Main page
app.get('/', (req, res) => {
    console.log('🏠 DEBUG: Main page requested');
    res.sendFile(path.join(__dirname, 'index.html'));
});

console.log('✅ DEBUG: Routes configured');

// Test database connection before starting server
async function testDatabaseConnection() {
    console.log('🧪 DEBUG: Testing database connection...');
    try {
        const result = await pool.query('SELECT 1 as test');
        console.log('✅ DEBUG: Database test successful');
        return true;
    } catch (error) {
        console.error('❌ DEBUG: Database test failed:', error.message);
        return false;
    }
}

// Start server
async function startServer() {
    console.log('🚀 DEBUG: Starting server...');
    
    try {
        // Test database first
        const dbConnected = await testDatabaseConnection();
        if (!dbConnected) {
            console.error('❌ DEBUG: Cannot start server - database connection failed');
            process.exit(1);
        }

        console.log('🌐 DEBUG: Starting HTTP server...');
        const server = app.listen(PORT, () => {
            console.log('🎉 DEBUG: Server started successfully!');
            console.log(`🚀 PostgreSQL Inventory System running on http://localhost:${PORT}`);
            console.log(`📊 Dashboard: http://localhost:${PORT}`);
            console.log(`🔧 API Health: http://localhost:${PORT}/api/health`);
            console.log(`📦 Products API: http://localhost:${PORT}/api/products`);
            console.log(`🗄️  Database: PostgreSQL (${process.env.DB_NAME || 'inventory_management'})`);
        });

        server.on('error', (error) => {
            console.error('❌ DEBUG: Server error:', error.message);
        });

    } catch (error) {
        console.error('❌ DEBUG: Server startup failed:', error.message);
        process.exit(1);
    }
}

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🔄 DEBUG: Shutting down gracefully...');
    await pool.end();
    console.log('✅ DEBUG: Database connections closed');
    process.exit(0);
});

// Start the server
console.log('🎬 DEBUG: Calling startServer()...');
startServer().catch(error => {
    console.error('❌ DEBUG: Startup error:', error);
    process.exit(1);
});

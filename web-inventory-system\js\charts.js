// Charts and Visualization Management
class ChartManager {
    constructor() {
        this.charts = {};
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#2ed573',
            warning: '#ffa502',
            danger: '#ff4757',
            info: '#4facfe',
            light: '#f8f9fa',
            dark: '#343a40'
        };
        this.gradients = {};
    }

    // Create gradient backgrounds
    createGradient(ctx, color1, color2) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        return gradient;
    }

    // Dashboard Charts
    createSalesChart(canvasId, salesData) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        // Process sales data by date
        const salesByDate = {};
        salesData.forEach(sale => {
            const date = new Date(sale.date).toLocaleDateString();
            salesByDate[date] = (salesByDate[date] || 0) + sale.totalAmount;
        });

        const labels = Object.keys(salesByDate).slice(-7);
        const data = Object.values(salesByDate).slice(-7);

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Daily Sales',
                    data: data,
                    borderColor: this.colors.primary,
                    backgroundColor: this.createGradient(ctx, 'rgba(102, 126, 234, 0.2)', 'rgba(102, 126, 234, 0.05)'),
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: this.colors.primary,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: this.colors.primary,
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                return 'Sales: ₹' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            color: '#666',
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    createStockChart(canvasId, products) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        const labels = products.slice(0, 6).map(p => p.name.length > 12 ? p.name.substring(0, 12) + '...' : p.name);
        const stockData = products.slice(0, 6).map(p => p.currentStock);
        const reorderLevels = products.slice(0, 6).map(p => p.reorderLevel);

        // Color code based on stock levels
        const backgroundColors = products.slice(0, 6).map(p => {
            if (p.currentStock <= p.reorderLevel) return this.colors.danger;
            if (p.currentStock <= p.reorderLevel * 1.5) return this.colors.warning;
            return this.colors.success;
        });

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Current Stock',
                    data: stockData,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors,
                    borderWidth: 1,
                    borderRadius: 8,
                    borderSkipped: false
                }, {
                    label: 'Reorder Level',
                    data: reorderLevels,
                    type: 'line',
                    borderColor: this.colors.danger,
                    backgroundColor: 'transparent',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    pointRadius: 0,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return 'Stock: ' + context.parsed.y + ' units';
                                } else {
                                    return 'Reorder Level: ' + context.parsed.y + ' units';
                                }
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                }
            }
        });
    }

    // Analytics Charts
    createTopCustomersChart(canvasId, salesData) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        // Calculate sales by customer
        const customerSales = {};
        salesData.forEach(sale => {
            customerSales[sale.customerName] = (customerSales[sale.customerName] || 0) + sale.totalAmount;
        });

        // Get top 5 customers
        const sortedCustomers = Object.entries(customerSales)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5);

        const labels = sortedCustomers.map(([name]) => name.length > 15 ? name.substring(0, 15) + '...' : name);
        const data = sortedCustomers.map(([,amount]) => amount);

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        this.colors.primary,
                        this.colors.success,
                        this.colors.warning,
                        this.colors.info,
                        this.colors.secondary
                    ],
                    borderWidth: 0,
                    hoverBorderWidth: 3,
                    hoverBorderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return context.label + ': ₹' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
    }

    createProductPerformanceChart(canvasId, salesData) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        // Calculate sales by product
        const productSales = {};
        salesData.forEach(sale => {
            productSales[sale.productName] = (productSales[sale.productName] || 0) + sale.quantity;
        });

        // Get top 8 products
        const sortedProducts = Object.entries(productSales)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 8);

        const labels = sortedProducts.map(([name]) => name.length > 12 ? name.substring(0, 12) + '...' : name);
        const data = sortedProducts.map(([,quantity]) => quantity);

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'horizontalBar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Units Sold',
                    data: data,
                    backgroundColor: this.createGradient(ctx, this.colors.primary, this.colors.secondary),
                    borderColor: this.colors.primary,
                    borderWidth: 1,
                    borderRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: function(context) {
                                return 'Sold: ' + context.parsed.x + ' units';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    }
                }
            }
        });
    }

    createVendorAnalysisChart(canvasId, purchaseData) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        // Calculate purchases by vendor
        const vendorPurchases = {};
        purchaseData.forEach(purchase => {
            vendorPurchases[purchase.vendor] = (vendorPurchases[purchase.vendor] || 0) + purchase.totalAmount;
        });

        const labels = Object.keys(vendorPurchases);
        const data = Object.values(vendorPurchases);

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(46, 213, 115, 0.8)',
                        'rgba(255, 165, 2, 0.8)',
                        'rgba(255, 71, 87, 0.8)',
                        'rgba(79, 172, 254, 0.8)'
                    ],
                    borderColor: [
                        this.colors.primary,
                        this.colors.success,
                        this.colors.warning,
                        this.colors.danger,
                        this.colors.info
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ₹' + context.parsed.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });
    }

    createMonthlyTrendsChart(canvasId, salesData, purchaseData) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        // Process monthly data
        const monthlySales = {};
        const monthlyPurchases = {};
        
        salesData.forEach(sale => {
            const month = new Date(sale.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
            monthlySales[month] = (monthlySales[month] || 0) + sale.totalAmount;
        });
        
        purchaseData.forEach(purchase => {
            const month = new Date(purchase.date).toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
            monthlyPurchases[month] = (monthlyPurchases[month] || 0) + purchase.totalAmount;
        });

        // Get last 6 months
        const months = [...new Set([...Object.keys(monthlySales), ...Object.keys(monthlyPurchases)])].slice(-6);
        const salesValues = months.map(month => monthlySales[month] || 0);
        const purchaseValues = months.map(month => monthlyPurchases[month] || 0);

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: months,
                datasets: [{
                    label: 'Sales',
                    data: salesValues,
                    borderColor: this.colors.success,
                    backgroundColor: 'rgba(46, 213, 115, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Purchases',
                    data: purchaseValues,
                    borderColor: this.colors.warning,
                    backgroundColor: 'rgba(255, 165, 2, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₹' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#666'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            color: '#666',
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    // Utility methods
    destroyChart(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }
    }

    destroyAllCharts() {
        Object.keys(this.charts).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }

    updateChart(canvasId, newData) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].data = newData;
            this.charts[canvasId].update();
        }
    }

    // Export chart as image
    exportChart(canvasId, filename = 'chart.png') {
        if (this.charts[canvasId]) {
            const canvas = this.charts[canvasId].canvas;
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
    }
}

// Initialize chart manager
const chartManager = new ChartManager();

// Global chart functions
function loadDashboardCharts() {
    const salesData = dataManager.getSales();
    const products = dataManager.getProducts();
    
    chartManager.createSalesChart('salesChart', salesData);
    chartManager.createStockChart('stockChart', products);
}

function loadAnalyticsCharts() {
    const salesData = dataManager.getSales();
    const purchaseData = dataManager.getPurchases();
    
    chartManager.createTopCustomersChart('topCustomersChart', salesData);
    chartManager.createProductPerformanceChart('productPerformanceChart', salesData);
    chartManager.createVendorAnalysisChart('vendorAnalysisChart', purchaseData);
    chartManager.createMonthlyTrendsChart('monthlyTrendsChart', salesData, purchaseData);
}

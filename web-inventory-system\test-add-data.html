<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Data - Inventory Management</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Add Data to All 14 Tables</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Add Vendor</h5>
                    </div>
                    <div class="card-body">
                        <form id="vendorForm">
                            <div class="mb-3">
                                <label class="form-label">Company</label>
                                <input type="number" class="form-control" name="company" value="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Vendor Code</label>
                                <input type="number" class="form-control" name="vendor_code" value="1001" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Vendor Name</label>
                                <input type="text" class="form-control" name="vendor_name" value="Test Vendor Ltd" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">City</label>
                                <input type="text" class="form-control" name="city" value="Mumbai">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Created By</label>
                                <input type="text" class="form-control" name="created_by" value="ADMIN">
                            </div>
                            <button type="submit" class="btn btn-primary">Add Vendor</button>
                        </form>
                        <div id="vendorResult" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Add Customer</h5>
                    </div>
                    <div class="card-body">
                        <form id="customerForm">
                            <div class="mb-3">
                                <label class="form-label">Company</label>
                                <input type="number" class="form-control" name="company" value="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Customer Code</label>
                                <input type="number" class="form-control" name="customer_code" value="2001" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Customer Name</label>
                                <input type="text" class="form-control" name="customer_name" value="Test Customer Ltd" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">City</label>
                                <input type="text" class="form-control" name="city" value="Delhi">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="<EMAIL>">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Created By</label>
                                <input type="text" class="form-control" name="created_by" value="ADMIN">
                            </div>
                            <button type="submit" class="btn btn-primary">Add Customer</button>
                        </form>
                        <div id="customerResult" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Add Unit</h5>
                    </div>
                    <div class="card-body">
                        <form id="unitForm">
                            <div class="mb-3">
                                <label class="form-label">Company</label>
                                <input type="number" class="form-control" name="company" value="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Unit ID</label>
                                <input type="number" class="form-control" name="unit_id" value="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Unit Description</label>
                                <input type="text" class="form-control" name="unit_desc" value="Pieces" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Created By</label>
                                <input type="text" class="form-control" name="created_by" value="ADMIN">
                            </div>
                            <button type="submit" class="btn btn-primary">Add Unit</button>
                        </form>
                        <div id="unitResult" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Add Part</h5>
                    </div>
                    <div class="card-body">
                        <form id="partForm">
                            <div class="mb-3">
                                <label class="form-label">Company</label>
                                <input type="number" class="form-control" name="company" value="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Part Number</label>
                                <input type="number" class="form-control" name="part_num" value="3001" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Part Description</label>
                                <input type="text" class="form-control" name="part_desc" value="Test Part" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Purchase Price</label>
                                <input type="number" class="form-control" name="pur_price" value="100" step="0.01">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Selling Price</label>
                                <input type="number" class="form-control" name="sell_price" value="120" step="0.01">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Created By</label>
                                <input type="text" class="form-control" name="created_by" value="ADMIN">
                            </div>
                            <button type="submit" class="btn btn-primary">Add Part</button>
                        </form>
                        <div id="partResult" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Test All APIs</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info mb-2" onclick="testAllAPIs()">Test All 14 Table APIs</button>
                        <div id="apiTestResults" class="mt-3"></div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5>View Data</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success mb-2" onclick="loadVendors()">Load Vendors</button>
                        <button class="btn btn-success mb-2" onclick="loadCustomers()">Load Customers</button>
                        <button class="btn btn-success mb-2" onclick="loadParts()">Load Parts</button>
                        <div id="dataResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api';

        // Handle vendor form
        document.getElementById('vendorForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            data.active = true;
            
            try {
                const response = await fetch(`${API_BASE}/vendors`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                document.getElementById('vendorResult').innerHTML = 
                    `<div class="alert alert-${result.success ? 'success' : 'danger'}">${JSON.stringify(result)}</div>`;
            } catch (error) {
                document.getElementById('vendorResult').innerHTML = 
                    `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        });

        // Handle customer form
        document.getElementById('customerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            data.active = true;
            
            try {
                const response = await fetch(`${API_BASE}/customers`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                document.getElementById('customerResult').innerHTML = 
                    `<div class="alert alert-${result.success ? 'success' : 'danger'}">${JSON.stringify(result)}</div>`;
            } catch (error) {
                document.getElementById('customerResult').innerHTML = 
                    `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        });

        // Handle unit form
        document.getElementById('unitForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            data.active = true;
            
            try {
                const response = await fetch(`${API_BASE}/units`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                document.getElementById('unitResult').innerHTML = 
                    `<div class="alert alert-${result.success ? 'success' : 'danger'}">${JSON.stringify(result)}</div>`;
            } catch (error) {
                document.getElementById('unitResult').innerHTML = 
                    `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        });

        // Handle part form
        document.getElementById('partForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            data.active = true;
            
            try {
                const response = await fetch(`${API_BASE}/parts`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                document.getElementById('partResult').innerHTML = 
                    `<div class="alert alert-${result.success ? 'success' : 'danger'}">${JSON.stringify(result)}</div>`;
            } catch (error) {
                document.getElementById('partResult').innerHTML = 
                    `<div class="alert alert-danger">Error: ${error.message}</div>`;
            }
        });

        // Test all APIs
        async function testAllAPIs() {
            const endpoints = [
                'vendors', 'customers', 'parts', 'units', 'users', 'user-roles', 'taxes',
                'indents', 'purchase-orders', 'goods-receipts', 'sales-invoices',
                'accounts-payable', 'accounts-receivable', 'stock-updates'
            ];
            
            let results = '<h6>API Test Results:</h6>';
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE}/${endpoint}`);
                    const result = await response.json();
                    const count = result.success ? result.data.length : 0;
                    results += `<div class="badge bg-${result.success ? 'success' : 'danger'} me-2 mb-1">${endpoint}: ${count} records</div>`;
                } catch (error) {
                    results += `<div class="badge bg-danger me-2 mb-1">${endpoint}: Error</div>`;
                }
            }
            
            document.getElementById('apiTestResults').innerHTML = results;
        }

        // Load vendors
        async function loadVendors() {
            try {
                const response = await fetch(`${API_BASE}/vendors`);
                const result = await response.json();
                document.getElementById('dataResults').innerHTML = 
                    `<h6>Vendors (${result.data?.length || 0}):</h6><pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('dataResults').innerHTML = 
                    `<div class="alert alert-danger">Error loading vendors: ${error.message}</div>`;
            }
        }

        // Load customers
        async function loadCustomers() {
            try {
                const response = await fetch(`${API_BASE}/customers`);
                const result = await response.json();
                document.getElementById('dataResults').innerHTML = 
                    `<h6>Customers (${result.data?.length || 0}):</h6><pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('dataResults').innerHTML = 
                    `<div class="alert alert-danger">Error loading customers: ${error.message}</div>`;
            }
        }

        // Load parts
        async function loadParts() {
            try {
                const response = await fetch(`${API_BASE}/parts`);
                const result = await response.json();
                document.getElementById('dataResults').innerHTML = 
                    `<h6>Parts (${result.data?.length || 0}):</h6><pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('dataResults').innerHTML = 
                    `<div class="alert alert-danger">Error loading parts: ${error.message}</div>`;
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testAllAPIs, 1000);
        });
    </script>
</body>
</html>

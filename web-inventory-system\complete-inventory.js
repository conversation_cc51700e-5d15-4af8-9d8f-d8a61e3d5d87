// Complete Inventory Management System - Frontend JavaScript
// Handles all 14 tables with full CRUD operations

const API_BASE_URL = 'http://localhost:3000/api';
let currentSection = 'vendors';
let currentData = [];
let editingId = null;

// Table configurations for all 14 tables
const tableConfigs = {
    vendors: {
        title: 'Vendor Management',
        icon: 'fas fa-truck',
        endpoint: 'vendors',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'vendor_code', label: 'Vendor Code', type: 'number', required: true },
            { name: 'vendor_name', label: 'Vendor Name', type: 'text', required: true },
            { name: 'address1', label: 'Address 1', type: 'text' },
            { name: 'address2', label: 'Address 2', type: 'text' },
            { name: 'location', label: 'Location', type: 'text' },
            { name: 'city', label: 'City', type: 'text' },
            { name: 'state', label: 'State', type: 'text' },
            { name: 'pin', label: 'PIN Code', type: 'number' },
            { name: 'std_code', label: 'STD Code', type: 'number' },
            { name: 'phone', label: 'Phone', type: 'number' },
            { name: 'mobile', label: 'Mobile', type: 'number' },
            { name: 'email', label: 'Email', type: 'email' },
            { name: 'website', label: 'Website', type: 'url' },
            { name: 'gst', label: 'GST Number', type: 'text' },
            { name: 'pan', label: 'PAN Number', type: 'text' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'vendor_code', 'vendor_name', 'city', 'phone', 'email', 'active']
    },
    customers: {
        title: 'Customer Management',
        icon: 'fas fa-users',
        endpoint: 'customers',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'customer_code', label: 'Customer Code', type: 'number', required: true },
            { name: 'customer_name', label: 'Customer Name', type: 'text', required: true },
            { name: 'address1', label: 'Address 1', type: 'text' },
            { name: 'address2', label: 'Address 2', type: 'text' },
            { name: 'location', label: 'Location', type: 'text' },
            { name: 'city', label: 'City', type: 'text' },
            { name: 'state', label: 'State', type: 'text' },
            { name: 'pin', label: 'PIN Code', type: 'number' },
            { name: 'std_code', label: 'STD Code', type: 'number' },
            { name: 'phone', label: 'Phone', type: 'number' },
            { name: 'mobile', label: 'Mobile', type: 'number' },
            { name: 'email', label: 'Email', type: 'email' },
            { name: 'website', label: 'Website', type: 'url' },
            { name: 'gst', label: 'GST Number', type: 'text' },
            { name: 'pan', label: 'PAN Number', type: 'text' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'customer_code', 'customer_name', 'city', 'phone', 'email', 'active']
    },
    parts: {
        title: 'Part Management',
        icon: 'fas fa-cogs',
        endpoint: 'parts',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'part_num', label: 'Part Number', type: 'number', required: true },
            { name: 'part_desc', label: 'Part Description', type: 'text', required: true },
            { name: 'part_type', label: 'Part Type', type: 'text' },
            { name: 'part_category', label: 'Part Category', type: 'text' },
            { name: 'unit_id', label: 'Unit ID', type: 'number' },
            { name: 'pur_price', label: 'Purchase Price', type: 'number' },
            { name: 'sell_price', label: 'Selling Price', type: 'number' },
            { name: 'hsn_code', label: 'HSN Code', type: 'text' },
            { name: 'tax_id', label: 'Tax ID', type: 'number' },
            { name: 'tax_percent', label: 'Tax Percent', type: 'number' },
            { name: 'lot', label: 'Lot Tracking', type: 'checkbox' },
            { name: 'batch', label: 'Batch Tracking', type: 'checkbox' },
            { name: 'warranty', label: 'Warranty', type: 'checkbox' },
            { name: 'costing', label: 'Costing Method', type: 'text' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'part_num', 'part_desc', 'part_type', 'pur_price', 'sell_price', 'active']
    },
    units: {
        title: 'Unit Management',
        icon: 'fas fa-ruler',
        endpoint: 'units',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'unit_id', label: 'Unit ID', type: 'number', required: true },
            { name: 'unit_desc', label: 'Unit Description', type: 'text', required: true },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'unit_id', 'unit_desc', 'active']
    },
    users: {
        title: 'User Management',
        icon: 'fas fa-user',
        endpoint: 'users',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'user_id', label: 'User ID', type: 'text', required: true },
            { name: 'user_key', label: 'User Key', type: 'text' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'user_id', 'user_key', 'active']
    },
    'user-roles': {
        title: 'User Role Management',
        icon: 'fas fa-user-shield',
        endpoint: 'user-roles',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'role_id', label: 'Role ID', type: 'number', required: true },
            { name: 'role_desc', label: 'Role Description', type: 'text', required: true },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'role_id', 'role_desc', 'active']
    },
    taxes: {
        title: 'Tax Management',
        icon: 'fas fa-percentage',
        endpoint: 'taxes',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'tax_id', label: 'Tax ID', type: 'number', required: true },
            { name: 'tax_desc', label: 'Tax Description', type: 'text', required: true },
            { name: 'tax_percent', label: 'Tax Percent', type: 'number' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'tax_id', 'tax_desc', 'tax_percent', 'active']
    },
    indents: {
        title: 'Indent/Request Management',
        icon: 'fas fa-clipboard-list',
        endpoint: 'indents',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'indent_id', label: 'Indent ID', type: 'number', required: true },
            { name: 'indent_date', label: 'Indent Date', type: 'date' },
            { name: 'indent_by', label: 'Indent By', type: 'text' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'indent_id', 'indent_date', 'indent_by', 'active']
    },
    'purchase-orders': {
        title: 'Purchase Order Management',
        icon: 'fas fa-shopping-cart',
        endpoint: 'purchase-orders',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'po_no', label: 'PO Number', type: 'number', required: true },
            { name: 'po_date', label: 'PO Date', type: 'date' },
            { name: 'vendor', label: 'Vendor', type: 'number' },
            { name: 'indent_id', label: 'Indent ID', type: 'number' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'po_no', 'po_date', 'vendor', 'active']
    },
    'goods-receipts': {
        title: 'Goods Receipt Management',
        icon: 'fas fa-box-open',
        endpoint: 'goods-receipts',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'grn_no', label: 'GRN Number', type: 'number', required: true },
            { name: 'grn_dt', label: 'GRN Date', type: 'date' },
            { name: 'vendor', label: 'Vendor', type: 'number' },
            { name: 'po_no', label: 'PO Number', type: 'number' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'grn_no', 'grn_dt', 'vendor', 'po_no', 'active']
    },
    'sales-invoices': {
        title: 'Sales Invoice Management',
        icon: 'fas fa-file-invoice',
        endpoint: 'sales-invoices',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'inv_no', label: 'Invoice Number', type: 'number', required: true },
            { name: 'inv_dt', label: 'Invoice Date', type: 'date' },
            { name: 'customer', label: 'Customer', type: 'number' },
            { name: 'po_no', label: 'PO Number', type: 'number' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'inv_no', 'inv_dt', 'customer', 'po_no', 'active']
    },
    'accounts-payable': {
        title: 'Accounts Payable Management',
        icon: 'fas fa-money-bill-wave',
        endpoint: 'accounts-payable',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'ap_id', label: 'AP ID', type: 'number', required: true },
            { name: 'ap_date', label: 'AP Date', type: 'date' },
            { name: 'vendor_code', label: 'Vendor Code', type: 'number' },
            { name: 'vendor_invoice', label: 'Vendor Invoice', type: 'text' },
            { name: 'invoice_due_date', label: 'Invoice Due Date', type: 'date' },
            { name: 'po_no', label: 'PO Number', type: 'number' },
            { name: 'amount', label: 'Amount', type: 'number' },
            { name: 'paid_amount', label: 'Paid Amount', type: 'number' },
            { name: 'due_amount', label: 'Due Amount', type: 'number' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'ap_id', 'vendor_code', 'amount', 'paid_amount', 'due_amount', 'active']
    },
    'accounts-receivable': {
        title: 'Accounts Receivable Management',
        icon: 'fas fa-hand-holding-usd',
        endpoint: 'accounts-receivable',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'ar_id', label: 'AR ID', type: 'number', required: true },
            { name: 'ar_date', label: 'AR Date', type: 'date' },
            { name: 'customer_code', label: 'Customer Code', type: 'number' },
            { name: 'invoice_number', label: 'Invoice Number', type: 'text' },
            { name: 'invoice_due_date', label: 'Invoice Due Date', type: 'date' },
            { name: 'po_no', label: 'PO Number', type: 'text' },
            { name: 'amount', label: 'Amount', type: 'number' },
            { name: 'paid_amount', label: 'Paid Amount', type: 'number' },
            { name: 'due_amount', label: 'Due Amount', type: 'number' },
            { name: 'active', label: 'Active', type: 'checkbox' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'ar_id', 'customer_code', 'amount', 'paid_amount', 'due_amount', 'active']
    },
    'stock-updates': {
        title: 'Stock Update Management',
        icon: 'fas fa-warehouse',
        endpoint: 'stock-updates',
        fields: [
            { name: 'company', label: 'Company', type: 'number', required: true },
            { name: 'update_id', label: 'Update ID', type: 'number', required: true },
            { name: 'update_date', label: 'Update Date', type: 'date' },
            { name: 'type', label: 'Type', type: 'select', options: ['IN', 'OUT', 'ADJ'] },
            { name: 'qty', label: 'Quantity', type: 'number' },
            { name: 'created_by', label: 'Created By', type: 'text' }
        ],
        displayFields: ['company', 'update_id', 'update_date', 'type', 'qty']
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Complete Inventory Management System Initialized');
    loadDashboardStats();
    loadData('vendors');
});

// Show specific section
function showSection(section) {
    // Update active nav link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    event.target.classList.add('active');
    
    currentSection = section;
    loadData(section);
}

// Load data for specific section
async function loadData(section) {
    const config = tableConfigs[section];
    if (!config) {
        console.error('Unknown section:', section);
        return;
    }

    showLoading(true);
    
    try {
        const response = await fetch(`${API_BASE_URL}/${config.endpoint}`);
        const result = await response.json();
        
        if (result.success) {
            currentData = result.data;
            renderSection(section, result.data);
        } else {
            showError('Failed to load data: ' + result.error);
        }
    } catch (error) {
        console.error('Error loading data:', error);
        showError('Network error: ' + error.message);
    } finally {
        showLoading(false);
    }
}

// Render section with data
function renderSection(section, data) {
    const config = tableConfigs[section];
    const contentDiv = document.getElementById('content-sections');
    
    contentDiv.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="${config.icon} me-2"></i>
                    ${config.title}
                </h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <button class="btn btn-primary" onclick="showAddForm('${section}')">
                            <i class="fas fa-plus me-2"></i>Add New ${config.title.replace(' Management', '')}
                        </button>
                    </div>
                    <div class="col-md-6">
                        <input type="text" class="form-control" placeholder="Search..." id="${section}Search" onkeyup="filterTable('${section}')">
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                ${config.displayFields.map(field => `<th>${field.replace('_', ' ').toUpperCase()}</th>`).join('')}
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="${section}TableBody">
                            ${renderTableRows(section, data)}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;
}

// Render table rows
function renderTableRows(section, data) {
    const config = tableConfigs[section];
    
    return data.map(item => `
        <tr>
            ${config.displayFields.map(field => {
                let value = item[field];
                if (field === 'active') {
                    value = `<span class="status-badge ${value ? 'status-active' : 'status-inactive'}">${value ? 'Active' : 'Inactive'}</span>`;
                }
                return `<td>${value || '-'}</td>`;
            }).join('')}
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editRecord('${section}', '${getRecordId(item, config)}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteRecord('${section}', '${getRecordId(item, config)}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

// Get record ID based on table structure
function getRecordId(item, config) {
    // For composite keys, we'll use the first unique field
    const firstField = config.fields.find(f => f.name.includes('_id') || f.name.includes('_code') || f.name.includes('_no'));
    return item[firstField?.name] || item.id;
}

// Show loading indicator
function showLoading(show) {
    const loading = document.getElementById('loading');
    if (loading) {
        loading.style.display = show ? 'block' : 'none';
    }
}

// Show error message
function showError(message) {
    alert('Error: ' + message);
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Load counts for each major table
        const endpoints = ['vendors', 'purchase-orders', 'accounts-payable', 'stock-updates'];
        
        for (const endpoint of endpoints) {
            const response = await fetch(`${API_BASE_URL}/${endpoint}`);
            const result = await response.json();
            
            if (result.success) {
                const count = result.data.length;
                const elementId = endpoint.replace('-', '') + 'Count';
                const element = document.getElementById(elementId);
                if (element) {
                    element.textContent = count;
                }
            }
        }
    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

// Show add form modal
function showAddForm(section) {
    const config = tableConfigs[section];
    editingId = null;
    
    document.getElementById('modalTitle').textContent = `Add New ${config.title.replace(' Management', '')}`;
    generateFormFields(config.fields);
    
    const modal = new bootstrap.Modal(document.getElementById('dataModal'));
    modal.show();
}

// Generate form fields dynamically
function generateFormFields(fields) {
    const formFields = document.getElementById('formFields');
    
    formFields.innerHTML = fields.map(field => {
        let inputHtml = '';
        
        switch (field.type) {
            case 'checkbox':
                inputHtml = `<input type="checkbox" class="form-check-input" id="${field.name}" name="${field.name}">`;
                break;
            case 'select':
                inputHtml = `
                    <select class="form-select" id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>
                        <option value="">Select ${field.label}</option>
                        ${field.options ? field.options.map(opt => `<option value="${opt}">${opt}</option>`).join('') : ''}
                    </select>
                `;
                break;
            default:
                inputHtml = `<input type="${field.type}" class="form-control" id="${field.name}" name="${field.name}" ${field.required ? 'required' : ''}>`;
        }
        
        return `
            <div class="mb-3">
                <label for="${field.name}" class="form-label">${field.label} ${field.required ? '<span class="text-danger">*</span>' : ''}</label>
                ${inputHtml}
            </div>
        `;
    }).join('');
}

// Save data (add or edit)
async function saveData() {
    const form = document.getElementById('dataForm');
    const formData = new FormData(form);
    const data = {};
    
    // Convert FormData to object
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    // Handle checkboxes
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(cb => {
        data[cb.name] = cb.checked;
    });
    
    const config = tableConfigs[currentSection];
    const url = `${API_BASE_URL}/${config.endpoint}`;
    const method = editingId ? 'PUT' : 'POST';
    
    try {
        const response = await fetch(editingId ? `${url}/${editingId}` : url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('dataModal'));
            modal.hide();
            loadData(currentSection);
            showSuccess(editingId ? 'Record updated successfully!' : 'Record added successfully!');
        } else {
            showError('Failed to save: ' + result.error);
        }
    } catch (error) {
        console.error('Error saving data:', error);
        showError('Network error: ' + error.message);
    }
}

// Show success message
function showSuccess(message) {
    // Simple alert for now - can be enhanced with toast notifications
    alert('Success: ' + message);
}

// Filter table data
function filterTable(section) {
    const searchInput = document.getElementById(section + 'Search');
    const searchTerm = searchInput.value.toLowerCase();
    
    const filteredData = currentData.filter(item => {
        return Object.values(item).some(value => 
            value && value.toString().toLowerCase().includes(searchTerm)
        );
    });
    
    const tableBody = document.getElementById(section + 'TableBody');
    tableBody.innerHTML = renderTableRows(section, filteredData);
}

// Edit record
function editRecord(section, id) {
    const config = tableConfigs[section];
    const record = currentData.find(item => getRecordId(item, config) == id);
    
    if (!record) {
        showError('Record not found');
        return;
    }
    
    editingId = id;
    document.getElementById('modalTitle').textContent = `Edit ${config.title.replace(' Management', '')}`;
    generateFormFields(config.fields);
    
    // Populate form with existing data
    config.fields.forEach(field => {
        const element = document.getElementById(field.name);
        if (element) {
            if (field.type === 'checkbox') {
                element.checked = record[field.name];
            } else {
                element.value = record[field.name] || '';
            }
        }
    });
    
    const modal = new bootstrap.Modal(document.getElementById('dataModal'));
    modal.show();
}

// Delete record
async function deleteRecord(section, id) {
    if (!confirm('Are you sure you want to delete this record?')) {
        return;
    }
    
    const config = tableConfigs[section];
    
    try {
        const response = await fetch(`${API_BASE_URL}/${config.endpoint}/${id}`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            loadData(currentSection);
            showSuccess('Record deleted successfully!');
        } else {
            showError('Failed to delete: ' + result.error);
        }
    } catch (error) {
        console.error('Error deleting data:', error);
        showError('Network error: ' + error.message);
    }
}

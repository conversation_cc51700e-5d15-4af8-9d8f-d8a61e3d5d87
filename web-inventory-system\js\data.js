// Sample Data for the Inventory Management System
const sampleData = {
    products: [
        {
            id: 'P1001',
            hsnCode: 'P1001',
            name: 'Gaming Laptop',
            category: 'Electronics',
            costPrice: 45000,
            sellingPrice: 52000,
            supplier: 'TechCorp',
            reorderLevel: 5,
            maxStockLevel: 50,
            currentStock: 12
        },
        {
            id: 'P1002',
            hsnCode: 'P1002',
            name: 'Wireless Mouse',
            category: 'Accessories',
            costPrice: 1200,
            sellingPrice: 1440,
            supplier: 'AccessoryHub',
            reorderLevel: 20,
            maxStockLevel: 100,
            currentStock: 45
        },
        {
            id: 'P1003',
            hsnCode: 'P1003',
            name: 'Mechanical Keyboard',
            category: 'Accessories',
            costPrice: 3500,
            sellingPrice: 4200,
            supplier: 'AccessoryHub',
            reorderLevel: 15,
            maxStockLevel: 80,
            currentStock: 23
        },
        {
            id: 'P1004',
            hsnCode: 'P1004',
            name: '4K Monitor',
            category: 'Electronics',
            costPrice: 25000,
            sellingPrice: 30000,
            supplier: 'DisplayTech',
            reorderLevel: 8,
            maxStockLevel: 40,
            currentStock: 6
        },
        {
            id: 'P1005',
            hsnCode: 'P1005',
            name: 'Webcam HD',
            category: 'Electronics',
            costPrice: 4500,
            sellingPrice: 5400,
            supplier: 'TechCorp',
            reorderLevel: 12,
            maxStockLevel: 60,
            currentStock: 3
        },
        {
            id: 'P1006',
            hsnCode: 'P1006',
            name: 'Bluetooth Speaker',
            category: 'Electronics',
            costPrice: 2800,
            sellingPrice: 3360,
            supplier: 'AudioMax',
            reorderLevel: 10,
            maxStockLevel: 50,
            currentStock: 18
        },
        {
            id: 'P1007',
            hsnCode: 'P1007',
            name: 'External SSD 1TB',
            category: 'Storage',
            costPrice: 8500,
            sellingPrice: 10200,
            supplier: 'StoragePro',
            reorderLevel: 15,
            maxStockLevel: 75,
            currentStock: 28
        },
        {
            id: 'P1008',
            hsnCode: 'P1008',
            name: 'Graphics Card',
            category: 'Components',
            costPrice: 35000,
            sellingPrice: 42000,
            supplier: 'ComponentKing',
            reorderLevel: 6,
            maxStockLevel: 30,
            currentStock: 4
        },
        {
            id: 'P1009',
            hsnCode: 'P1009',
            name: 'Processor Intel i7',
            category: 'Components',
            costPrice: 28000,
            sellingPrice: 33600,
            supplier: 'ComponentKing',
            reorderLevel: 8,
            maxStockLevel: 40,
            currentStock: 2
        },
        {
            id: 'P1010',
            hsnCode: 'P1010',
            name: 'RAM 16GB DDR4',
            category: 'Components',
            costPrice: 6500,
            sellingPrice: 7800,
            supplier: 'ComponentKing',
            reorderLevel: 12,
            maxStockLevel: 60,
            currentStock: 35
        }
    ],

    vendors: [
        {
            id: 'V001',
            name: 'TechCorp',
            contactPerson: 'Rajesh Kumar',
            phone: '9876543210',
            email: '<EMAIL>',
            address: 'Mumbai, Maharashtra',
            paymentTerms: '30 Days',
            rating: 4.5
        },
        {
            id: 'V002',
            name: 'AccessoryHub',
            contactPerson: 'Priya Sharma',
            phone: '9876543211',
            email: '<EMAIL>',
            address: 'Delhi, NCR',
            paymentTerms: '15 Days',
            rating: 4.2
        },
        {
            id: 'V003',
            name: 'DisplayTech',
            contactPerson: 'Amit Singh',
            phone: '9876543212',
            email: '<EMAIL>',
            address: 'Bangalore, Karnataka',
            paymentTerms: '45 Days',
            rating: 4.7
        },
        {
            id: 'V004',
            name: 'AudioMax',
            contactPerson: 'Neha Gupta',
            phone: '9876543213',
            email: '<EMAIL>',
            address: 'Pune, Maharashtra',
            paymentTerms: '30 Days',
            rating: 4.3
        },
        {
            id: 'V005',
            name: 'StoragePro',
            contactPerson: 'Vikram Patel',
            phone: '9876543214',
            email: '<EMAIL>',
            address: 'Chennai, Tamil Nadu',
            paymentTerms: '30 Days',
            rating: 4.6
        }
    ],

    customers: [
        {
            id: 'C001',
            name: 'Tech Solutions Ltd',
            contactPerson: 'Suresh Reddy',
            phone: '9123456789',
            email: '<EMAIL>',
            address: 'Hyderabad, Telangana',
            type: 'B2B',
            creditLimit: 500000
        },
        {
            id: 'C002',
            name: 'Gaming Zone',
            contactPerson: 'Anita Joshi',
            phone: '9123456790',
            email: '<EMAIL>',
            address: 'Mumbai, Maharashtra',
            type: 'B2C',
            creditLimit: 100000
        },
        {
            id: 'C003',
            name: 'Office Supplies Co',
            contactPerson: 'Rohit Mehta',
            phone: '9123456791',
            email: '<EMAIL>',
            address: 'Delhi, NCR',
            type: 'B2B',
            creditLimit: 300000
        },
        {
            id: 'C004',
            name: 'Digital World',
            contactPerson: 'Kavya Nair',
            phone: '9123456792',
            email: '<EMAIL>',
            address: 'Kochi, Kerala',
            type: 'B2C',
            creditLimit: 150000
        },
        {
            id: 'C005',
            name: 'Smart Systems',
            contactPerson: 'Deepak Agarwal',
            phone: '9123456793',
            email: '<EMAIL>',
            address: 'Jaipur, Rajasthan',
            type: 'B2B',
            creditLimit: 400000
        },
        {
            id: 'C006',
            name: 'Cyber Cafe Network',
            contactPerson: 'Sanjay Kumar',
            phone: '9123456794',
            email: '<EMAIL>',
            address: 'Kolkata, West Bengal',
            type: 'B2B',
            creditLimit: 250000
        },
        {
            id: 'C007',
            name: 'Educational Institute',
            contactPerson: 'Dr. Meera Shah',
            phone: '9123456795',
            email: '<EMAIL>',
            address: 'Pune, Maharashtra',
            type: 'B2B',
            creditLimit: 600000
        },
        {
            id: 'C008',
            name: 'Startup Hub',
            contactPerson: 'Arjun Kapoor',
            phone: '9123456796',
            email: '<EMAIL>',
            address: 'Bangalore, Karnataka',
            type: 'B2B',
            creditLimit: 350000
        }
    ],

    purchases: [
        {
            id: 'PUR1001',
            date: '2024-01-15',
            productId: 'P1001',
            productName: 'Gaming Laptop',
            vendor: 'TechCorp',
            quantity: 20,
            unitCost: 45000,
            totalAmount: 900000,
            status: 'Received'
        },
        {
            id: 'PUR1002',
            date: '2024-01-16',
            productId: 'P1002',
            productName: 'Wireless Mouse',
            vendor: 'AccessoryHub',
            quantity: 50,
            unitCost: 1200,
            totalAmount: 60000,
            status: 'Received'
        },
        {
            id: 'PUR1003',
            date: '2024-01-17',
            productId: 'P1008',
            productName: 'Graphics Card',
            vendor: 'ComponentKing',
            quantity: 10,
            unitCost: 35000,
            totalAmount: 350000,
            status: 'Pending'
        },
        {
            id: 'PUR1004',
            date: '2024-01-18',
            productId: 'P1004',
            productName: '4K Monitor',
            vendor: 'DisplayTech',
            quantity: 15,
            unitCost: 25000,
            totalAmount: 375000,
            status: 'Received'
        },
        {
            id: 'PUR1005',
            date: '2024-01-19',
            productId: 'P1007',
            productName: 'External SSD 1TB',
            vendor: 'StoragePro',
            quantity: 30,
            unitCost: 8500,
            totalAmount: 255000,
            status: 'Received'
        }
    ],

    sales: [
        {
            id: 'SAL2001',
            date: '2024-01-20',
            customerId: 'C001',
            customerName: 'Tech Solutions Ltd',
            productId: 'P1001',
            productName: 'Gaming Laptop',
            quantity: 5,
            unitPrice: 52000,
            totalAmount: 260000,
            paymentStatus: 'Paid',
            paymentMethod: 'Bank Transfer'
        },
        {
            id: 'SAL2002',
            date: '2024-01-21',
            customerId: 'C002',
            customerName: 'Gaming Zone',
            productId: 'P1002',
            productName: 'Wireless Mouse',
            quantity: 10,
            unitPrice: 1440,
            totalAmount: 14400,
            paymentStatus: 'Paid',
            paymentMethod: 'Cash'
        },
        {
            id: 'SAL2003',
            date: '2024-01-22',
            customerId: 'C003',
            customerName: 'Office Supplies Co',
            productId: 'P1003',
            productName: 'Mechanical Keyboard',
            quantity: 15,
            unitPrice: 4200,
            totalAmount: 63000,
            paymentStatus: 'Pending',
            paymentMethod: 'Credit'
        },
        {
            id: 'SAL2004',
            date: '2024-01-23',
            customerId: 'C004',
            customerName: 'Digital World',
            productId: 'P1004',
            productName: '4K Monitor',
            quantity: 3,
            unitPrice: 30000,
            totalAmount: 90000,
            paymentStatus: 'Paid',
            paymentMethod: 'UPI'
        },
        {
            id: 'SAL2005',
            date: '2024-01-24',
            customerId: 'C005',
            customerName: 'Smart Systems',
            productId: 'P1008',
            productName: 'Graphics Card',
            quantity: 2,
            unitPrice: 42000,
            totalAmount: 84000,
            paymentStatus: 'Partial',
            paymentMethod: 'Bank Transfer'
        }
    ]
};

// Data Management Functions
class DataManager {
    constructor() {
        this.data = this.loadData();
    }

    loadData() {
        // Try to load from localStorage, fallback to sample data
        const savedData = localStorage.getItem('inventoryData');
        if (savedData) {
            return JSON.parse(savedData);
        }
        return JSON.parse(JSON.stringify(sampleData)); // Deep copy
    }

    saveData() {
        localStorage.setItem('inventoryData', JSON.stringify(this.data));
    }

    // Product methods
    getProducts() {
        return this.data.products;
    }

    getProduct(id) {
        return this.data.products.find(p => p.id === id);
    }

    addProduct(product) {
        product.id = this.generateId('P');
        product.hsnCode = product.id;
        this.data.products.push(product);
        this.saveData();
        return product;
    }

    updateProduct(id, updates) {
        const index = this.data.products.findIndex(p => p.id === id);
        if (index !== -1) {
            this.data.products[index] = { ...this.data.products[index], ...updates };
            this.saveData();
            return this.data.products[index];
        }
        return null;
    }

    deleteProduct(id) {
        const index = this.data.products.findIndex(p => p.id === id);
        if (index !== -1) {
            this.data.products.splice(index, 1);
            this.saveData();
            return true;
        }
        return false;
    }

    // Vendor methods
    getVendors() {
        return this.data.vendors;
    }

    addVendor(vendor) {
        vendor.id = this.generateId('V');
        this.data.vendors.push(vendor);
        this.saveData();
        return vendor;
    }

    // Customer methods
    getCustomers() {
        return this.data.customers;
    }

    addCustomer(customer) {
        customer.id = this.generateId('C');
        this.data.customers.push(customer);
        this.saveData();
        return customer;
    }

    // Purchase methods
    getPurchases() {
        return this.data.purchases;
    }

    addPurchase(purchase) {
        purchase.id = this.generateId('PUR');
        this.data.purchases.push(purchase);
        
        // Update product stock
        const product = this.getProduct(purchase.productId);
        if (product) {
            product.currentStock += purchase.quantity;
            this.saveData();
        }
        
        return purchase;
    }

    // Sales methods
    getSales() {
        return this.data.sales;
    }

    addSale(sale) {
        sale.id = this.generateId('SAL');
        this.data.sales.push(sale);
        
        // Update product stock
        const product = this.getProduct(sale.productId);
        if (product && product.currentStock >= sale.quantity) {
            product.currentStock -= sale.quantity;
            this.saveData();
        }
        
        return sale;
    }

    // Inventory methods
    getInventory() {
        return this.data.products.map(product => {
            const stockValue = product.currentStock * product.costPrice;
            let status = 'Adequate';
            
            if (product.currentStock <= product.reorderLevel) {
                status = 'Critical';
            } else if (product.currentStock <= product.reorderLevel * 1.5) {
                status = 'Low Stock';
            }

            return {
                ...product,
                stockValue,
                status,
                lastUpdated: new Date().toISOString()
            };
        });
    }

    // Analytics methods
    getAnalytics() {
        const totalSales = this.data.sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
        const totalPurchases = this.data.purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0);
        const totalInventoryValue = this.data.products.reduce((sum, product) => 
            sum + (product.currentStock * product.costPrice), 0);

        const criticalStock = this.data.products.filter(p => p.currentStock <= p.reorderLevel);
        const lowStock = this.data.products.filter(p => 
            p.currentStock > p.reorderLevel && p.currentStock <= p.reorderLevel * 1.5);

        return {
            totalProducts: this.data.products.length,
            totalCustomers: this.data.customers.length,
            totalVendors: this.data.vendors.length,
            totalSales,
            totalPurchases,
            totalInventoryValue,
            criticalStockCount: criticalStock.length,
            lowStockCount: lowStock.length,
            criticalStockItems: criticalStock,
            lowStockItems: lowStock
        };
    }

    // Utility methods
    generateId(prefix) {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000);
        return `${prefix}${timestamp}${random}`;
    }

    // Search and filter methods
    searchProducts(query) {
        const lowercaseQuery = query.toLowerCase();
        return this.data.products.filter(product =>
            product.name.toLowerCase().includes(lowercaseQuery) ||
            product.category.toLowerCase().includes(lowercaseQuery) ||
            product.supplier.toLowerCase().includes(lowercaseQuery)
        );
    }

    filterProductsByCategory(category) {
        if (!category) return this.data.products;
        return this.data.products.filter(product => product.category === category);
    }
}

// Initialize data manager
const dataManager = new DataManager();

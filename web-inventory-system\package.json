{"name": "advanced-inventory-management-system", "version": "1.0.0", "description": "A comprehensive web-based inventory management system with 9 interactive screens", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install", "setup": "npm install && node setup.js", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["inventory", "management", "system", "web", "dashboard", "analytics", "business", "stock", "sales", "purchase"], "author": "AI Assistant", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/inventory-management-system.git"}, "bugs": {"url": "https://github.com/your-username/inventory-management-system/issues"}, "homepage": "https://github.com/your-username/inventory-management-system#readme"}
// Check Current Database Structure
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function checkDatabase() {
    console.log('🔍 Checking Current Database Structure...\n');

    try {
        // Test connection
        await pool.query('SELECT 1');
        console.log('✅ Connected to PostgreSQL\n');

        // Check existing tables
        const result = await pool.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);

        console.log('📋 Current Tables in Database:');
        console.log('================================');
        
        if (result.rows.length === 0) {
            console.log('❌ No tables found in database!');
        } else {
            for (const row of result.rows) {
                const tableName = row.table_name;
                try {
                    const countResult = await pool.query(`SELECT COUNT(*) FROM ${tableName}`);
                    const count = countResult.rows[0].count;
                    console.log(`✅ ${tableName.toUpperCase()}: ${count} records`);
                } catch (error) {
                    console.log(`❌ ${tableName.toUpperCase()}: Error accessing table`);
                }
            }
        }

        console.log('\n🎯 Required Tables (14 total):');
        console.log('==============================');
        const requiredTables = [
            'vendor', 'customer', 'part', 'unit', 'user', 'user_role', 'tax',
            'indent_request', 'purchase_order', 'goods_receipt', 'sales_invoice',
            'accounts_payable', 'accounts_receivable', 'stock_update'
        ];

        const existingTables = result.rows.map(row => row.table_name);
        
        for (const table of requiredTables) {
            const exists = existingTables.includes(table);
            console.log(`${exists ? '✅' : '❌'} ${table.toUpperCase()}: ${exists ? 'EXISTS' : 'MISSING'}`);
        }

        const missingTables = requiredTables.filter(table => !existingTables.includes(table));
        
        if (missingTables.length > 0) {
            console.log(`\n⚠️  Missing ${missingTables.length} tables: ${missingTables.join(', ')}`);
            console.log('\n🔧 Need to create missing tables...');
        } else {
            console.log('\n🎉 All 14 required tables exist!');
        }

    } catch (error) {
        console.error('❌ Database check failed:', error.message);
    } finally {
        await pool.end();
    }
}

checkDatabase();

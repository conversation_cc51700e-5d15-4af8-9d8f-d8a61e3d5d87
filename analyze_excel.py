import pandas as pd
import numpy as np
from pathlib import Path

def clean_and_analyze_sheet(file_path, sheet_name):
    """
    Clean and analyze a specific sheet with proper headers
    """
    print(f"\n{'='*60}")
    print(f"DETAILED ANALYSIS: {sheet_name}")
    print(f"{'='*60}")

    try:
        # Read the sheet
        df = pd.read_excel(file_path, sheet_name=sheet_name)

        # Try to find the actual header row by looking for non-null values
        header_row = None
        for i in range(min(10, len(df))):  # Check first 10 rows
            row = df.iloc[i]
            non_null_count = row.notna().sum()
            if non_null_count >= 3:  # At least 3 non-null values
                header_row = i
                break

        if header_row is not None and header_row > 0:
            # Re-read with proper header
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
            print(f"Found header at row {header_row}")

        # Clean the dataframe
        df = df.dropna(how='all')  # Remove completely empty rows
        df = df.dropna(axis=1, how='all')  # Remove completely empty columns

        print(f"Shape after cleaning: {df.shape} (rows x columns)")
        print(f"Columns: {list(df.columns)}")

        if len(df) == 0:
            print("No data found after cleaning")
            return None

        # Display the cleaned data
        print(f"\nCleaned Data (first 10 rows):")
        print(df.head(10).to_string())

        if len(df) > 10:
            print(f"\nLast 5 rows:")
            print(df.tail().to_string())

        # Data types and missing values
        print(f"\nData Types and Missing Values:")
        for col in df.columns:
            missing_count = df[col].isnull().sum()
            missing_pct = (missing_count / len(df)) * 100
            print(f"  {col}: {df[col].dtype} | Missing: {missing_count} ({missing_pct:.1f}%)")

        # Summary statistics for numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            print(f"\nNumeric Summary:")
            print(df[numeric_cols].describe().to_string())

        return df

    except Exception as e:
        print(f"Error analyzing sheet {sheet_name}: {str(e)}")
        return None

def analyze_excel_file(file_path):
    """
    Comprehensive analysis of the Excel file
    """
    print("=" * 80)
    print("INVENTORY MANAGEMENT SYSTEM - EXCEL FILE ANALYSIS")
    print("=" * 80)

    try:
        # Read the Excel file
        excel_file = pd.ExcelFile(file_path)

        print(f"File: {file_path}")
        print(f"Number of sheets: {len(excel_file.sheet_names)}")
        print(f"Sheet names: {excel_file.sheet_names}")

        # Store cleaned dataframes
        cleaned_sheets = {}

        # Analyze each sheet with proper cleaning
        for sheet_name in excel_file.sheet_names:
            cleaned_df = clean_and_analyze_sheet(file_path, sheet_name)
            if cleaned_df is not None and len(cleaned_df) > 0:
                cleaned_sheets[sheet_name] = cleaned_df

        # Generate insights
        print(f"\n{'='*80}")
        print("BUSINESS INSIGHTS")
        print(f"{'='*80}")

        generate_business_insights(cleaned_sheets)

        return cleaned_sheets

    except Exception as e:
        print(f"Error analyzing file: {str(e)}")
        return None

def generate_business_insights(sheets):
    """
    Generate business insights from the cleaned data
    """
    try:
        # Analyze Products
        if 'Products' in sheets:
            products_df = sheets['Products']
            print(f"\n📦 PRODUCT ANALYSIS:")
            print(f"   • Total products: {len(products_df)}")
            if 'Cost' in products_df.columns and 'Selling Price' in products_df.columns:
                try:
                    cost_col = pd.to_numeric(products_df['Cost'], errors='coerce')
                    price_col = pd.to_numeric(products_df['Selling Price'], errors='coerce')
                    avg_margin = ((price_col - cost_col) / cost_col * 100).mean()
                    print(f"   • Average profit margin: {avg_margin:.1f}%")
                    print(f"   • Price range: ${cost_col.min():.2f} - ${price_col.max():.2f}")
                except:
                    pass

        # Analyze Inventory
        if 'Inventory' in sheets:
            inventory_df = sheets['Inventory']
            print(f"\n📊 INVENTORY ANALYSIS:")
            print(f"   • Items in inventory: {len(inventory_df)}")

            # Low stock alerts
            if 'Stock' in inventory_df.columns:
                try:
                    stock_col = pd.to_numeric(inventory_df['Stock'], errors='coerce')
                    low_stock = stock_col[stock_col <= 5].count()
                    print(f"   • Low stock items (≤5 units): {low_stock}")

                    if 'Product Name' in inventory_df.columns:
                        low_stock_items = inventory_df[stock_col <= 5]['Product Name'].tolist()
                        if low_stock_items:
                            print(f"   • Items needing reorder: {', '.join(low_stock_items)}")
                except:
                    pass

        # Analyze Sales
        if 'Sales' in sheets:
            sales_df = sheets['Sales']
            print(f"\n💰 SALES ANALYSIS:")
            print(f"   • Total sales transactions: {len(sales_df)}")

            if 'Amount' in sales_df.columns:
                try:
                    amount_col = pd.to_numeric(sales_df['Amount'], errors='coerce')
                    total_sales = amount_col.sum()
                    avg_sale = amount_col.mean()
                    print(f"   • Total sales value: ${total_sales:,.2f}")
                    print(f"   • Average sale amount: ${avg_sale:,.2f}")
                except:
                    pass

            if 'Cust_Name' in sales_df.columns:
                top_customers = sales_df['Cust_Name'].value_counts().head(3)
                print(f"   • Top customers: {', '.join(top_customers.index.tolist())}")

        # Analyze Purchase
        if 'Purchase' in sheets:
            purchase_df = sheets['Purchase']
            print(f"\n🛒 PURCHASE ANALYSIS:")
            print(f"   • Total purchase transactions: {len(purchase_df)}")

            if 'Amount' in purchase_df.columns:
                try:
                    amount_col = pd.to_numeric(purchase_df['Amount'], errors='coerce')
                    total_purchases = amount_col.sum()
                    print(f"   • Total purchase value: ${total_purchases:,.2f}")
                except:
                    pass

            if 'Vendor' in purchase_df.columns:
                top_vendors = purchase_df['Vendor'].value_counts().head(3)
                print(f"   • Top vendors: {', '.join(top_vendors.index.tolist())}")

        # Analyze Customers
        if 'Customers' in sheets:
            customers_df = sheets['Customers']
            print(f"\n👥 CUSTOMER ANALYSIS:")
            print(f"   • Total customers: {len(customers_df)}")

        # Analyze Vendors
        if 'Vendors' in sheets:
            vendors_df = sheets['Vendors']
            print(f"\n🏢 VENDOR ANALYSIS:")
            print(f"   • Total vendors: {len(vendors_df)}")

            if 'Vendor Name' in vendors_df.columns:
                vendor_counts = vendors_df['Vendor Name'].value_counts()
                print(f"   • Vendor distribution: {dict(vendor_counts)}")

    except Exception as e:
        print(f"Error generating insights: {str(e)}")

if __name__ == "__main__":
    # File path
    file_path = "inventory management software by deepak eduworld.xlsx"
    
    # Check if file exists
    if Path(file_path).exists():
        analyze_excel_file(file_path)
    else:
        print(f"File not found: {file_path}")
        print("Available files in current directory:")
        for file in Path(".").glob("*.xlsx"):
            print(f"  {file}")

// Database Diagnostic Script
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function diagnoseDatabase() {
    console.log('🔍 DIAGNOSING DATABASE CONNECTION AND DATA...\n');
    
    try {
        // Test basic connection
        console.log('1. Testing PostgreSQL connection...');
        const connectionTest = await pool.query('SELECT NOW() as current_time, version() as version');
        console.log('✅ Connection successful!');
        console.log(`   Time: ${connectionTest.rows[0].current_time}`);
        console.log(`   Version: ${connectionTest.rows[0].version.split(' ').slice(0, 2).join(' ')}\n`);

        // Check if database exists and is accessible
        console.log('2. Checking database access...');
        const dbCheck = await pool.query('SELECT current_database() as db_name');
        console.log(`✅ Connected to database: ${dbCheck.rows[0].db_name}\n`);

        // Check if tables exist
        console.log('3. Checking table structure...');
        const tablesQuery = `
            SELECT table_name, table_type 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `;
        const tablesResult = await pool.query(tablesQuery);
        
        if (tablesResult.rows.length === 0) {
            console.log('❌ No tables found! Database needs to be set up.');
            console.log('   Run: npm run setup:postgresql\n');
            return;
        }

        console.log('✅ Tables found:');
        tablesResult.rows.forEach(table => {
            console.log(`   • ${table.table_name} (${table.table_type})`);
        });
        console.log('');

        // Check data in each table
        console.log('4. Checking data in tables...');
        const dataTables = ['categories', 'vendors', 'customers', 'products', 'purchases', 'sales', 'stock_movements'];
        
        for (const tableName of dataTables) {
            try {
                const countResult = await pool.query(`SELECT COUNT(*) as count FROM ${tableName}`);
                const count = parseInt(countResult.rows[0].count);
                
                if (count > 0) {
                    console.log(`✅ ${tableName}: ${count} records`);
                } else {
                    console.log(`⚠️  ${tableName}: 0 records (empty)`);
                }
            } catch (error) {
                console.log(`❌ ${tableName}: Table doesn't exist or error - ${error.message}`);
            }
        }
        console.log('');

        // Check critical stock alerts
        console.log('5. Checking inventory status...');
        try {
            const inventoryQuery = `
                SELECT 
                    COUNT(*) as total_products,
                    COUNT(CASE WHEN current_stock <= reorder_level THEN 1 END) as critical_items,
                    COUNT(CASE WHEN current_stock > reorder_level AND current_stock <= reorder_level * 1.5 THEN 1 END) as low_stock_items
                FROM products 
                WHERE is_active = TRUE
            `;
            const inventoryResult = await pool.query(inventoryQuery);
            const stats = inventoryResult.rows[0];
            
            console.log(`✅ Inventory Status:`);
            console.log(`   • Total Products: ${stats.total_products}`);
            console.log(`   • Critical Stock: ${stats.critical_items}`);
            console.log(`   • Low Stock: ${stats.low_stock_items}`);
        } catch (error) {
            console.log(`❌ Error checking inventory: ${error.message}`);
        }
        console.log('');

        // Test API endpoints
        console.log('6. Testing API functionality...');
        try {
            // Test products endpoint
            const productsResult = await pool.query(`
                SELECT 
                    p.id,
                    p.product_code,
                    p.name,
                    c.name as category,
                    p.current_stock
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = TRUE
                LIMIT 3
            `);
            
            if (productsResult.rows.length > 0) {
                console.log('✅ Sample products data:');
                productsResult.rows.forEach(product => {
                    console.log(`   • ${product.name} (${product.product_code}) - Stock: ${product.current_stock}`);
                });
            } else {
                console.log('⚠️  No products found in database');
            }
        } catch (error) {
            console.log(`❌ Error testing API: ${error.message}`);
        }
        console.log('');

        // Check triggers and functions
        console.log('7. Checking database triggers...');
        try {
            const triggersQuery = `
                SELECT trigger_name, event_object_table, action_timing, event_manipulation
                FROM information_schema.triggers
                WHERE trigger_schema = 'public'
                ORDER BY trigger_name
            `;
            const triggersResult = await pool.query(triggersQuery);
            
            if (triggersResult.rows.length > 0) {
                console.log('✅ Database triggers:');
                triggersResult.rows.forEach(trigger => {
                    console.log(`   • ${trigger.trigger_name} on ${trigger.event_object_table}`);
                });
            } else {
                console.log('⚠️  No triggers found - automatic stock updates may not work');
            }
        } catch (error) {
            console.log(`❌ Error checking triggers: ${error.message}`);
        }
        console.log('');

        // Final recommendations
        console.log('🎯 DIAGNOSIS COMPLETE!\n');
        
        const totalTables = tablesResult.rows.length;
        const expectedTables = 7; // categories, vendors, customers, products, purchases, sales, stock_movements
        
        if (totalTables >= expectedTables) {
            console.log('✅ Database appears to be properly set up!');
            console.log('');
            console.log('🚀 Next steps:');
            console.log('   1. Start the PostgreSQL server: npm run start:postgresql');
            console.log('   2. Open the web app: http://localhost:3000');
            console.log('   3. Check pgAdmin4 for database management');
        } else {
            console.log('❌ Database setup incomplete!');
            console.log('');
            console.log('🔧 To fix this:');
            console.log('   1. Run database setup: npm run setup:postgresql');
            console.log('   2. Or manually run: node database/setup-database.js');
            console.log('   3. Check PostgreSQL service is running');
            console.log('   4. Verify credentials in .env file');
        }

    } catch (error) {
        console.error('❌ Database diagnosis failed:', error.message);
        console.error('');
        console.error('🔧 Troubleshooting steps:');
        console.error('   1. Check if PostgreSQL is running:');
        console.error('      Windows: Check Services or Task Manager');
        console.error('      Linux/Mac: sudo systemctl status postgresql');
        console.error('');
        console.error('   2. Verify database credentials in .env file:');
        console.error(`      DB_HOST=${process.env.DB_HOST || 'localhost'}`);
        console.error(`      DB_PORT=${process.env.DB_PORT || 5432}`);
        console.error(`      DB_USER=${process.env.DB_USER || 'postgres'}`);
        console.error(`      DB_NAME=${process.env.DB_NAME || 'inventory_management'}`);
        console.error('      DB_PASSWORD=[check your password]');
        console.error('');
        console.error('   3. Test connection manually:');
        console.error('      psql -h localhost -U postgres -d inventory_management');
        console.error('');
        console.error('   4. Create database if it doesn\'t exist:');
        console.error('      psql -U postgres -c "CREATE DATABASE inventory_management;"');
    } finally {
        await pool.end();
    }
}

// Run diagnosis
diagnoseDatabase().catch(console.error);

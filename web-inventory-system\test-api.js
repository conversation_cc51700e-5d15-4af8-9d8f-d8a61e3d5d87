// Test API endpoints
const http = require('http');

function testAPI(endpoint) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'localhost',
            port: 3000,
            path: endpoint,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const result = JSON.parse(data);
                    resolve(result);
                } catch (error) {
                    resolve({ error: 'Invalid JSON', data });
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.setTimeout(5000, () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });

        req.end();
    });
}

async function runTests() {
    console.log('🧪 Testing API endpoints...\n');

    try {
        // Test database connection
        console.log('1. Testing database connection...');
        const testResult = await testAPI('/api/test');
        if (testResult.success) {
            console.log(`✅ Database connected! Found ${testResult.productCount} products\n`);
        } else {
            console.log('❌ Database test failed:', testResult.error);
            return;
        }

        // Test products API
        console.log('2. Testing products API...');
        const productsResult = await testAPI('/api/products');
        if (productsResult.success && productsResult.data) {
            console.log(`✅ Products API working! Found ${productsResult.data.length} products`);
            console.log('📦 Sample products:');
            productsResult.data.slice(0, 3).forEach(product => {
                console.log(`   • ${product.name}: ${product.currentStock} units (${product.hsnCode})`);
            });
        } else {
            console.log('❌ Products API failed:', productsResult.error);
        }

        console.log('\n🎉 API tests complete!');
        console.log('🌐 Open your browser to: http://localhost:3000');

    } catch (error) {
        console.error('❌ API test failed:', error.message);
        console.log('\n💡 Make sure the server is running:');
        console.log('   node test-minimal-server.js');
    }
}

runTests();

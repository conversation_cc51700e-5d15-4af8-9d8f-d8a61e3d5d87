# 🚀 Advanced Inventory Management System

A comprehensive web-based inventory management system with 9 interactive screens, real-time analytics, and modern UI/UX design.

## 📋 Table of Contents

- [Features](#features)
- [Screenshots](#screenshots)
- [Installation](#installation)
- [Usage](#usage)
- [API Documentation](#api-documentation)
- [System Architecture](#system-architecture)
- [Contributing](#contributing)
- [License](#license)

## ✨ Features

### 🎯 **9 Comprehensive Screens**
1. **📊 Dashboard** - Real-time KPIs, charts, and critical alerts
2. **📦 Products** - Complete product catalog management
3. **🏢 Vendors** - Supplier database with ratings and contact info
4. **👥 Customers** - Client management with B2B/B2C segmentation
5. **🛒 Purchase** - Procurement tracking and status management
6. **💰 Sales** - Revenue transactions and payment tracking
7. **📊 Inventory** - Real-time stock monitoring with automated alerts
8. **📈 Analytics** - Business intelligence with interactive charts
9. **➕ New Entry** - Quick data entry forms and templates

### 🔥 **Advanced Capabilities**
- **Real-time Stock Monitoring** with automated alerts
- **Interactive Charts** using Chart.js
- **Responsive Design** for desktop and mobile
- **Offline Support** with local data storage
- **Search & Filter** functionality across all data
- **Export Capabilities** for reports and charts
- **RESTful API** for backend integration
- **Data Backup & Restore** functionality

### 🎨 **Modern UI/UX**
- **Material Design** inspired interface
- **Dark/Light Theme** support
- **Smooth Animations** and transitions
- **Mobile-First** responsive design
- **Accessibility** compliant (WCAG 2.1)

## 📸 Screenshots

### Dashboard Overview
![Dashboard](screenshots/dashboard.png)

### Product Management
![Products](screenshots/products.png)

### Analytics & Reports
![Analytics](screenshots/analytics.png)

## 🚀 Installation

### Prerequisites
- Node.js (v14.0.0 or higher)
- npm (v6.0.0 or higher)
- Modern web browser (Chrome, Firefox, Safari, Edge)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/inventory-management-system.git
   cd inventory-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the server**
   ```bash
   npm start
   ```

4. **Open your browser**
   ```
   http://localhost:3000
   ```

### Development Mode

For development with auto-reload:
```bash
npm run dev
```

## 📖 Usage

### Getting Started

1. **Access the Dashboard**
   - Open http://localhost:3000 in your browser
   - The dashboard shows real-time KPIs and alerts

2. **Add Your Data**
   - Start with **Vendors** - Add your suppliers
   - Add **Products** - Create your product catalog
   - Add **Customers** - Register your clients
   - Record **Purchases** and **Sales**

3. **Monitor Inventory**
   - Check the **Inventory** screen for stock levels
   - Set up reorder levels for automatic alerts
   - Use **Analytics** for business insights

### Key Workflows

#### Adding a New Product
1. Navigate to **Products** screen
2. Click **"Add Product"** button
3. Fill in product details:
   - Product name, category, pricing
   - Supplier, stock levels
   - Reorder thresholds
4. Save the product

#### Recording a Sale
1. Go to **Sales** screen
2. Click **"New Sale"** button
3. Select customer and product
4. Enter quantity and pricing
5. Choose payment method and status
6. System automatically updates inventory

#### Monitoring Stock Alerts
1. Dashboard shows critical alerts
2. **Inventory** screen shows detailed status
3. Color-coded indicators:
   - 🔴 Critical (≤ reorder level)
   - 🟡 Low Stock (≤ 1.5x reorder level)
   - 🟢 Adequate (> 1.5x reorder level)

## 🔧 API Documentation

### Base URL
```
http://localhost:3000/api
```

### Authentication
Currently, the API doesn't require authentication. In production, implement proper authentication.

### Endpoints

#### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get specific product
- `POST /api/products` - Create new product
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product

#### Vendors
- `GET /api/vendors` - Get all vendors
- `POST /api/vendors` - Create new vendor

#### Customers
- `GET /api/customers` - Get all customers
- `POST /api/customers` - Create new customer

#### Purchases
- `GET /api/purchases` - Get all purchases
- `POST /api/purchases` - Create new purchase

#### Sales
- `GET /api/sales` - Get all sales
- `POST /api/sales` - Create new sale

#### Inventory
- `GET /api/inventory` - Get inventory status
- `GET /api/inventory/alerts` - Get stock alerts

#### Analytics
- `GET /api/analytics` - Get business analytics

#### Search
- `GET /api/search/products?q=query` - Search products

### Example API Calls

#### Create a Product
```javascript
POST /api/products
Content-Type: application/json

{
  "name": "Gaming Laptop",
  "category": "Electronics",
  "costPrice": 45000,
  "sellingPrice": 52000,
  "supplier": "TechCorp",
  "reorderLevel": 5,
  "maxStockLevel": 50,
  "currentStock": 10
}
```

#### Get Stock Alerts
```javascript
GET /api/inventory/alerts

Response:
{
  "success": true,
  "data": [
    {
      "level": "critical",
      "message": "Gaming Laptop is critically low (2 units)",
      "productId": "P123",
      "currentStock": 2,
      "reorderLevel": 5
    }
  ]
}
```

## 🏗️ System Architecture

### Frontend Architecture
```
web-inventory-system/
├── index.html              # Main application entry
├── css/
│   └── styles.css          # Comprehensive styling
├── js/
│   ├── app.js             # Main application logic
│   ├── data.js            # Data management layer
│   ├── api.js             # API communication
│   ├── charts.js          # Chart management
│   └── modals.js          # Modal forms
└── server.js              # Backend server
```

### Backend Architecture
- **Express.js** server with RESTful API
- **File-based** data storage (JSON)
- **CORS** enabled for cross-origin requests
- **Error handling** and validation
- **Backup/Restore** functionality

### Data Flow
1. **Frontend** makes API calls to backend
2. **Backend** processes requests and updates data
3. **Real-time updates** via periodic polling
4. **Local storage** for offline capability
5. **Charts** update automatically with new data

## 📊 Data Models

### Product Model
```javascript
{
  id: "P123",
  hsnCode: "P123",
  name: "Gaming Laptop",
  category: "Electronics",
  costPrice: 45000,
  sellingPrice: 52000,
  supplier: "TechCorp",
  reorderLevel: 5,
  maxStockLevel: 50,
  currentStock: 10,
  createdAt: "2024-01-01T00:00:00Z"
}
```

### Sale Model
```javascript
{
  id: "SAL123",
  date: "2024-01-01",
  customerId: "C123",
  customerName: "Tech Solutions Ltd",
  productId: "P123",
  productName: "Gaming Laptop",
  quantity: 2,
  unitPrice: 52000,
  totalAmount: 104000,
  paymentStatus: "Paid",
  paymentMethod: "Bank Transfer"
}
```

## 🔒 Security Considerations

### Current Implementation
- Basic input validation
- CORS protection
- Error handling

### Production Recommendations
- Implement authentication (JWT)
- Add rate limiting
- Use HTTPS
- Validate all inputs
- Implement role-based access
- Add audit logging

## 🚀 Deployment

### Local Development
```bash
npm start
```

### Production Deployment

#### Using PM2
```bash
npm install -g pm2
pm2 start server.js --name inventory-system
pm2 startup
pm2 save
```

#### Using Docker
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

#### Environment Variables
```bash
PORT=3000
NODE_ENV=production
DATA_PATH=/data
BACKUP_PATH=/backups
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow JavaScript ES6+ standards
- Use meaningful variable names
- Add comments for complex logic
- Test all new features
- Update documentation

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Chart.js** for beautiful charts
- **Font Awesome** for icons
- **Express.js** for the backend framework
- **Modern CSS** techniques for responsive design

## 📞 Support

For support, email <EMAIL> or create an issue on GitHub.

---

**Made with ❤️ by AI Assistant**

*Transform your inventory management with this powerful, modern web application!*

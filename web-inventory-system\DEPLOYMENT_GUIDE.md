# 🚀 Deployment Guide - Advanced Inventory Management System

## 📋 Quick Start

### 1. **Local Development Setup**

```bash
# Clone or navigate to the project directory
cd web-inventory-system

# Install dependencies
npm install

# Run initial setup (creates sample data)
node setup.js

# Start the development server
npm start

# Open in browser
# http://localhost:3000
```

### 2. **Verify Installation**

After starting the server, you should see:
- ✅ Server running on http://localhost:3000
- ✅ Dashboard loads with sample data
- ✅ All 9 screens accessible via sidebar navigation
- ✅ Charts and analytics displaying properly

## 🌐 Production Deployment

### **Option 1: Traditional Server Deployment**

#### Prerequisites
- Ubuntu/CentOS server with root access
- Node.js 16+ and npm installed
- Domain name (optional)
- SSL certificate (recommended)

#### Steps

1. **Server Setup**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2
```

2. **Deploy Application**
```bash
# Upload files to server (using scp, git, etc.)
git clone https://github.com/your-repo/inventory-system.git
cd inventory-system

# Install dependencies
npm install --production

# Run setup
node setup.js

# Start with PM2
pm2 start server.js --name "inventory-system"
pm2 startup
pm2 save
```

3. **Configure Nginx (Optional)**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### **Option 2: Docker Deployment**

1. **Create Dockerfile**
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --production

# Copy application files
COPY . .

# Create data directory
RUN mkdir -p data logs

# Run setup
RUN node setup.js

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# Start application
CMD ["npm", "start"]
```

2. **Build and Run**
```bash
# Build image
docker build -t inventory-system .

# Run container
docker run -d \
  --name inventory-system \
  -p 3000:3000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  inventory-system
```

3. **Docker Compose (Recommended)**
```yaml
version: '3.8'

services:
  inventory-system:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - PORT=3000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - inventory-system
    restart: unless-stopped
```

### **Option 3: Cloud Platform Deployment**

#### **Heroku**
```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-inventory-system

# Set environment variables
heroku config:set NODE_ENV=production

# Deploy
git push heroku main

# Run setup
heroku run node setup.js
```

#### **Vercel**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "server.js",
      "use": "@vercel/node"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/server.js"
    }
  ]
}
```

#### **Railway**
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

## 🔧 Configuration

### **Environment Variables**

Create `.env` file:
```bash
# Server Configuration
PORT=3000
NODE_ENV=production

# Data Storage
DATA_PATH=./data
BACKUP_PATH=./backups

# Security
JWT_SECRET=your-secret-key
CORS_ORIGIN=https://your-domain.com

# Database (if using external DB)
DATABASE_URL=mongodb://localhost:27017/inventory

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-password

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
```

### **Production Optimizations**

1. **Enable Compression**
```javascript
const compression = require('compression');
app.use(compression());
```

2. **Security Headers**
```javascript
const helmet = require('helmet');
app.use(helmet());
```

3. **Rate Limiting**
```javascript
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use('/api', limiter);
```

## 📊 Monitoring & Maintenance

### **Health Monitoring**

1. **Application Health**
```bash
# Check application status
curl http://localhost:3000/api/health

# PM2 monitoring
pm2 monit

# View logs
pm2 logs inventory-system
```

2. **System Monitoring**
```bash
# Install monitoring tools
npm install -g clinic
npm install -g autocannon

# Performance testing
autocannon -c 10 -d 30 http://localhost:3000

# Memory profiling
clinic doctor -- node server.js
```

### **Backup Strategy**

1. **Automated Backups**
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DATA_DIR="/app/data"

# Create backup
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" "$DATA_DIR"

# Keep only last 30 days
find "$BACKUP_DIR" -name "backup_*.tar.gz" -mtime +30 -delete

echo "Backup completed: backup_$DATE.tar.gz"
```

2. **Cron Job Setup**
```bash
# Add to crontab
0 2 * * * /path/to/backup.sh >> /var/log/backup.log 2>&1
```

### **Log Management**

1. **Log Rotation**
```bash
# Install logrotate
sudo apt install logrotate

# Configure log rotation
sudo nano /etc/logrotate.d/inventory-system
```

```
/app/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 node node
    postrotate
        pm2 reload inventory-system
    endscript
}
```

## 🔒 Security Checklist

### **Pre-Production Security**

- [ ] **Authentication** - Implement user authentication
- [ ] **Authorization** - Add role-based access control
- [ ] **HTTPS** - Enable SSL/TLS encryption
- [ ] **Input Validation** - Validate all user inputs
- [ ] **Rate Limiting** - Prevent API abuse
- [ ] **CORS** - Configure proper CORS settings
- [ ] **Security Headers** - Add security headers
- [ ] **Data Encryption** - Encrypt sensitive data
- [ ] **Backup Encryption** - Encrypt backup files
- [ ] **Audit Logging** - Log all user actions

### **Security Configuration**

```javascript
// Enhanced security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com"],
      scriptSrc: ["'self'", "https://cdn.jsdelivr.net"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// CORS configuration
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  credentials: true,
  optionsSuccessStatus: 200
}));
```

## 📈 Performance Optimization

### **Frontend Optimization**

1. **Minification**
```bash
# Install build tools
npm install -g uglify-js clean-css-cli

# Minify JavaScript
uglifyjs js/*.js -o js/app.min.js

# Minify CSS
cleancss css/styles.css -o css/styles.min.css
```

2. **Caching Strategy**
```javascript
// Static file caching
app.use(express.static('.', {
  maxAge: '1d',
  etag: true
}));

// API response caching
const cache = require('memory-cache');
app.use('/api', (req, res, next) => {
  const key = req.originalUrl;
  const cached = cache.get(key);
  if (cached) {
    return res.json(cached);
  }
  next();
});
```

### **Database Optimization**

1. **Indexing** (if using database)
```javascript
// MongoDB indexes
db.products.createIndex({ name: "text", category: "text" });
db.sales.createIndex({ date: -1 });
db.inventory.createIndex({ currentStock: 1, reorderLevel: 1 });
```

2. **Query Optimization**
```javascript
// Pagination
app.get('/api/products', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;
  
  // Return paginated results
});
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Port Already in Use**
```bash
# Find process using port 3000
lsof -i :3000

# Kill process
kill -9 <PID>
```

2. **Permission Errors**
```bash
# Fix file permissions
chmod -R 755 /app
chown -R node:node /app
```

3. **Memory Issues**
```bash
# Increase Node.js memory limit
node --max-old-space-size=4096 server.js
```

4. **Database Connection Issues**
```bash
# Check database status
systemctl status mongodb

# Restart database
systemctl restart mongodb
```

### **Debug Mode**

```bash
# Enable debug logging
DEBUG=* npm start

# Or specific modules
DEBUG=express:* npm start
```

## 📞 Support & Maintenance

### **Regular Maintenance Tasks**

- **Weekly**: Check logs, monitor performance
- **Monthly**: Update dependencies, security patches
- **Quarterly**: Full backup verification, security audit
- **Annually**: System architecture review

### **Getting Help**

- 📧 **Email**: <EMAIL>
- 🐛 **Issues**: GitHub Issues
- 📖 **Documentation**: README.md
- 💬 **Community**: Discord/Slack channel

---

**🎉 Your Advanced Inventory Management System is now ready for production!**

*For additional support or custom deployment assistance, please contact our team.*

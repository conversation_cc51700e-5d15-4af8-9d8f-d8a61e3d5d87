-- Complete Inventory Management System - PostgreSQL Schema
-- Based on 14-table Excel design with multi-company support
-- Created for enterprise-level inventory management

-- <PERSON><PERSON> database (run this separately in pgAdmin4)
-- CREATE DATABASE inventory_management;

-- Connect to the database and run the following:

-- Enable UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types for better data integrity
CREATE TYPE customer_type AS ENUM ('B2B', 'B2C');
CREATE TYPE transaction_status AS ENUM ('Pending', 'Received', 'Partial', 'Cancelled');
CREATE TYPE payment_status AS ENUM ('Paid', 'Pending', 'Partial', 'Overdue');
CREATE TYPE payment_method AS ENUM ('Cash', 'Card', 'UPI', 'Bank Transfer', 'Credit', 'Cheque');
CREATE TYPE stock_status AS ENUM ('Critical', 'Low Stock', 'Adequate', 'Overstocked');
CREATE TYPE user_role_type AS ENUM ('Admin', 'Manager', 'User', 'Viewer');
CREATE TYPE update_type AS ENUM ('IN', 'OUT', 'ADJUSTMENT');

-- 1. VENDOR TABLE - Master Data
CREATE TABLE vendor (
    company NUMERIC NOT NULL,
    vendor_code NUMERIC UNIQUE NOT NULL,
    vendor_name VARCHAR(100) NOT NULL,
    address1 VARCHAR(200),
    address2 VARCHAR(200),
    location VARCHAR(100),
    city VARCHAR(100),
    state VARCHAR(100),
    pin NUMERIC,
    std_code NUMERIC,
    phone NUMERIC,
    mobile NUMERIC,
    email VARCHAR(100),
    website VARCHAR(200),
    gst VARCHAR(16),
    pan VARCHAR(10),
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, vendor_code)
);

-- 2. CUSTOMER TABLE - Master Data
CREATE TABLE customer (
    company NUMERIC NOT NULL,
    customer_code NUMERIC UNIQUE NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    address1 VARCHAR(200),
    address2 VARCHAR(200),
    location VARCHAR(100),
    city VARCHAR(100),
    state VARCHAR(100),
    pin NUMERIC,
    std_code NUMERIC,
    phone NUMERIC,
    mobile NUMERIC,
    email VARCHAR(100),
    website VARCHAR(200),
    gst VARCHAR(16),
    pan VARCHAR(10),
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, customer_code)
);

-- 3. PART TABLE - Master Data
CREATE TABLE part (
    company NUMERIC NOT NULL,
    part_num NUMERIC UNIQUE NOT NULL,
    part_desc VARCHAR(200) NOT NULL,
    part_type VARCHAR(100),
    part_category VARCHAR(100),
    unit_id NUMERIC,
    pur_price NUMERIC,
    sell_price NUMERIC,
    hsn_code VARCHAR(20),
    tax_id NUMERIC,
    tax_percent NUMERIC,
    lot BOOLEAN DEFAULT FALSE,
    batch BOOLEAN DEFAULT FALSE,
    warranty BOOLEAN DEFAULT FALSE,
    costing VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, part_num)
);

-- 4. UNIT TABLE - Master Data
CREATE TABLE unit (
    company NUMERIC NOT NULL,
    unit_id NUMERIC UNIQUE NOT NULL,
    unit_desc VARCHAR(100) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, unit_id)
);

-- 5. USER TABLE - Master Data
CREATE TABLE "user" (
    company NUMERIC NOT NULL,
    user_id VARCHAR(50) UNIQUE NOT NULL,
    user_key VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, user_id)
);

-- 6. USER ROLE TABLE - Master Data
CREATE TABLE user_role (
    company NUMERIC NOT NULL,
    role_id NUMERIC UNIQUE NOT NULL,
    role_desc VARCHAR(100) NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, role_id)
);

-- 7. TAX TABLE - Master Data
CREATE TABLE tax (
    company NUMERIC NOT NULL,
    tax_id NUMERIC UNIQUE NOT NULL,
    tax_desc VARCHAR(100) NOT NULL,
    tax_percent NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, tax_id)
);

-- 8. INDENT/REQUEST TABLE - Transaction Data
CREATE TABLE indent_request (
    company NUMERIC NOT NULL,
    indent_id NUMERIC UNIQUE NOT NULL,
    indent_date DATE DEFAULT CURRENT_DATE,
    indent_by VARCHAR(50),
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, indent_id)
);

-- Transaction Data on Tables (Header columns)
CREATE TABLE indent_request_data (
    company NUMERIC NOT NULL,
    indent_id NUMERIC NOT NULL,
    row_id NUMERIC NOT NULL,
    part_num NUMERIC,
    part_desc VARCHAR(200),
    indent_qty NUMERIC,
    unit VARCHAR(10),
    required_dt DATE,
    remarks TEXT,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, indent_id, row_id),
    FOREIGN KEY (company, indent_id) REFERENCES indent_request(company, indent_id)
);

-- 9. PURCHASE ORDER TABLE - Transaction Data
CREATE TABLE purchase_order (
    company NUMERIC NOT NULL,
    po_no NUMERIC UNIQUE NOT NULL,
    po_date DATE DEFAULT CURRENT_DATE,
    vendor NUMERIC,
    indent_id NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, po_no)
);

-- Transaction Data on Tables (Header columns)
CREATE TABLE purchase_order_data (
    company NUMERIC NOT NULL,
    po_no NUMERIC NOT NULL,
    row_id NUMERIC NOT NULL,
    part_num NUMERIC,
    part_desc VARCHAR(200),
    po_qty NUMERIC,
    unit VARCHAR(10),
    unit_price NUMERIC,
    tax_percent NUMERIC,
    amount NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, po_no, row_id),
    FOREIGN KEY (company, po_no) REFERENCES purchase_order(company, po_no)
);

-- 10. GOODS RECEIPT TABLE - Transaction Data
CREATE TABLE goods_receipt (
    company NUMERIC NOT NULL,
    grn_no NUMERIC UNIQUE NOT NULL,
    grn_dt DATE DEFAULT CURRENT_DATE,
    vendor NUMERIC,
    po_no NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, grn_no)
);

-- Transaction Data on Tables (Header columns)
CREATE TABLE goods_receipt_data (
    company NUMERIC NOT NULL,
    grn_no NUMERIC NOT NULL,
    row_id NUMERIC NOT NULL,
    part_num NUMERIC,
    part_desc VARCHAR(200),
    grn_qty NUMERIC,
    unit VARCHAR(10),
    unit_price NUMERIC,
    tax_percent NUMERIC,
    amount NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, grn_no, row_id),
    FOREIGN KEY (company, grn_no) REFERENCES goods_receipt(company, grn_no)
);

-- 11. SALES INVOICE TABLE - Transaction Data
CREATE TABLE sales_invoice (
    company NUMERIC NOT NULL,
    inv_no NUMERIC UNIQUE NOT NULL,
    inv_dt DATE DEFAULT CURRENT_DATE,
    customer NUMERIC,
    po_no NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, inv_no)
);

-- Transaction Data on Tables (Header columns)
CREATE TABLE sales_invoice_data (
    company NUMERIC NOT NULL,
    inv_no NUMERIC NOT NULL,
    row_id NUMERIC NOT NULL,
    part_num NUMERIC,
    part_desc VARCHAR(200),
    inv_qty NUMERIC,
    unit VARCHAR(10),
    unit_price NUMERIC,
    tax_percent NUMERIC,
    amount NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, inv_no, row_id),
    FOREIGN KEY (company, inv_no) REFERENCES sales_invoice(company, inv_no)
);



-- 12. ACCOUNTS PAYABLE TABLE - Status Update on Tables
CREATE TABLE accounts_payable (
    company NUMERIC NOT NULL,
    ap_id NUMERIC UNIQUE NOT NULL,
    ap_date DATE DEFAULT CURRENT_DATE,
    vendor_code NUMERIC,
    vendor_invoice VARCHAR(50),
    invoice_due_date DATE,
    po_no NUMERIC,
    amount NUMERIC,
    paid_amount NUMERIC,
    due_amount NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, ap_id)
);

-- 13. ACCOUNTS RECEIVABLE TABLE - Status Update on Tables
CREATE TABLE accounts_receivable (
    company NUMERIC NOT NULL,
    ar_id NUMERIC UNIQUE NOT NULL,
    ar_date DATE DEFAULT CURRENT_DATE,
    customer_code NUMERIC,
    invoice_number VARCHAR(50),
    invoice_due_date DATE,
    po_no VARCHAR(50),
    amount NUMERIC,
    paid_amount NUMERIC,
    due_amount NUMERIC,
    active BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, ar_id)
);

-- 14. STOCK UPDATE TABLE - Status Update on Tables
CREATE TABLE stock_update (
    company NUMERIC NOT NULL,
    update_id NUMERIC UNIQUE NOT NULL,
    update_date DATE DEFAULT CURRENT_DATE,
    type VARCHAR(10), -- 'IN', 'OUT', 'ADJ'
    qty NUMERIC,
    created_by VARCHAR(50),
    updated DATE DEFAULT CURRENT_DATE,
    PRIMARY KEY (company, update_id)
);

-- Create sequences for generating codes
CREATE SEQUENCE vendor_code_seq START 1001;
CREATE SEQUENCE customer_code_seq START 2001;
CREATE SEQUENCE indent_seq START 3001;
CREATE SEQUENCE po_seq START 4001;
CREATE SEQUENCE grn_seq START 5001;
CREATE SEQUENCE invoice_seq START 6001;
CREATE SEQUENCE ap_seq START 7001;
CREATE SEQUENCE ar_seq START 8001;
CREATE SEQUENCE stock_update_seq START 9001;

-- Create indexes for better performance
CREATE INDEX idx_vendor_company ON vendor(company);
CREATE INDEX idx_customer_company ON customer(company);
CREATE INDEX idx_part_company ON part(company);
CREATE INDEX idx_unit_company ON unit(company);
CREATE INDEX idx_user_company ON "user"(company);
CREATE INDEX idx_user_role_company ON user_role(company);
CREATE INDEX idx_tax_company ON tax(company);
CREATE INDEX idx_indent_company ON indent_request(company);
CREATE INDEX idx_po_company ON purchase_order(company);
CREATE INDEX idx_grn_company ON goods_receipt(company);
CREATE INDEX idx_invoice_company ON sales_invoice(company);
CREATE INDEX idx_ap_company ON accounts_payable(company);
CREATE INDEX idx_ar_company ON accounts_receivable(company);
CREATE INDEX idx_stock_update_company ON stock_update(company);

-- Comments for documentation
COMMENT ON TABLE vendor IS 'Vendor/Supplier master data with multi-company support';
COMMENT ON TABLE customer IS 'Customer master data with multi-company support';
COMMENT ON TABLE part IS 'Part/Product master data with pricing and specifications';
COMMENT ON TABLE unit IS 'Unit of measurement master data';
COMMENT ON TABLE "user" IS 'User master data for system access';
COMMENT ON TABLE user_role IS 'User role definitions for access control';
COMMENT ON TABLE tax IS 'Tax master data for calculations';
COMMENT ON TABLE indent_request IS 'Internal requisition/indent requests';
COMMENT ON TABLE purchase_order IS 'Purchase orders to vendors';
COMMENT ON TABLE goods_receipt IS 'Goods receipt notes for received items';
COMMENT ON TABLE sales_invoice IS 'Sales invoices to customers';
COMMENT ON TABLE accounts_payable IS 'Vendor payment tracking';
COMMENT ON TABLE accounts_receivable IS 'Customer payment tracking';
COMMENT ON TABLE stock_update IS 'Stock movement and adjustment tracking';

-- Create admin user (optional - for application use)
-- CREATE USER inventory_admin WITH PASSWORD 'secure_password_here';
-- GRANT ALL PRIVILEGES ON DATABASE inventory_management TO inventory_admin;
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO inventory_admin;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO inventory_admin;

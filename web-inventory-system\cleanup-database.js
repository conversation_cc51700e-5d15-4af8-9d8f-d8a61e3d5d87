// Remove Old 7 Tables and Clean Database
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function cleanupDatabase() {
    console.log('🧹 Cleaning Up Database - Removing Old 7 Tables...\n');

    try {
        await pool.query('SELECT 1');
        console.log('✅ Connected to PostgreSQL\n');

        // List current tables
        const currentTables = await pool.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);

        console.log('📋 Current Tables Before Cleanup:');
        console.log('==================================');
        currentTables.rows.forEach(row => {
            console.log(`• ${row.table_name}`);
        });

        // Drop old tables and views
        console.log('\n🗑️ Removing old tables and views...');
        
        const oldTablesToDrop = [
            'DROP VIEW IF EXISTS inventory_status CASCADE',
            'DROP VIEW IF EXISTS sales_summary CASCADE', 
            'DROP VIEW IF EXISTS purchase_summary CASCADE',
            'DROP TABLE IF EXISTS stock_movements CASCADE',
            'DROP TABLE IF EXISTS sales CASCADE',
            'DROP TABLE IF EXISTS purchases CASCADE',
            'DROP TABLE IF EXISTS products CASCADE',
            'DROP TABLE IF EXISTS categories CASCADE',
            'DROP TABLE IF EXISTS customers CASCADE',
            'DROP TABLE IF EXISTS vendors CASCADE'
        ];

        for (const dropStmt of oldTablesToDrop) {
            try {
                await pool.query(dropStmt);
                const tableName = dropStmt.split(' ')[4]?.replace('IF EXISTS', '').trim();
                console.log(`✅ Dropped: ${tableName}`);
            } catch (error) {
                console.log(`⚠️  ${dropStmt}: ${error.message}`);
            }
        }

        // Drop old sequences
        console.log('\n🗑️ Removing old sequences...');
        const oldSequences = [
            'DROP SEQUENCE IF EXISTS vendor_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS customer_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS product_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS purchase_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS sale_code_seq CASCADE'
        ];

        for (const dropSeq of oldSequences) {
            try {
                await pool.query(dropSeq);
                console.log(`✅ Dropped sequence`);
            } catch (error) {
                console.log(`⚠️  ${dropSeq}: ${error.message}`);
            }
        }

        // Verify remaining tables (should be only our 14 new tables)
        console.log('\n🔍 Remaining Tables After Cleanup:');
        console.log('===================================');
        
        const remainingTables = await pool.query(`
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        `);

        const expectedTables = [
            'vendor', 'customer', 'part', 'unit', 'user', 'user_role', 'tax',
            'indent_request', 'purchase_order', 'goods_receipt', 'sales_invoice',
            'accounts_payable', 'accounts_receivable', 'stock_update'
        ];

        let allCorrect = true;
        for (const table of expectedTables) {
            const exists = remainingTables.rows.some(row => row.table_name === table);
            console.log(`${exists ? '✅' : '❌'} ${table.toUpperCase()}: ${exists ? 'EXISTS' : 'MISSING'}`);
            if (!exists) allCorrect = false;
        }

        // Check for any unexpected tables
        const unexpectedTables = remainingTables.rows.filter(row => 
            !expectedTables.includes(row.table_name) && 
            !row.table_name.endsWith('_data') // Allow transaction data tables
        );

        if (unexpectedTables.length > 0) {
            console.log('\n⚠️  Unexpected tables found:');
            unexpectedTables.forEach(row => {
                console.log(`• ${row.table_name}`);
            });
        }

        // Show record counts for our 14 tables
        console.log('\n📊 Current Record Counts:');
        console.log('=========================');
        
        let totalRecords = 0;
        for (const table of expectedTables) {
            try {
                const result = await pool.query(`SELECT COUNT(*) FROM ${table === 'user' ? '"user"' : table}`);
                const count = parseInt(result.rows[0].count);
                totalRecords += count;
                console.log(`✅ ${table.toUpperCase()}: ${count} records`);
            } catch (error) {
                console.log(`❌ ${table.toUpperCase()}: Error - ${error.message}`);
            }
        }

        console.log(`\n🎯 Total Records: ${totalRecords}`);

        if (allCorrect) {
            console.log('\n🎉 Database Cleanup Completed Successfully!');
            console.log('\n📋 Summary:');
            console.log('   • All old 7 tables removed');
            console.log('   • All 14 new tables preserved');
            console.log('   • Database is clean and ready');
            console.log('   • Frontend will now work with new structure only');
        } else {
            console.log('\n⚠️  Some issues found - please check the output above');
        }

    } catch (error) {
        console.error('❌ Cleanup failed:', error);
    } finally {
        await pool.end();
    }
}

cleanupDatabase();

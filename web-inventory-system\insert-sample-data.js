// Insert Sample Data Script
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function insertSampleData() {
    console.log('📊 Inserting sample data...\n');

    try {
        // Insert categories
        console.log('1. Inserting categories...');
        const categories = [
            ['Electronics', 'Electronic devices and gadgets'],
            ['Accessories', 'Computer and electronic accessories'],
            ['Components', 'Computer hardware components'],
            ['Storage', 'Data storage devices'],
            ['Networking', 'Network equipment and accessories']
        ];

        for (const [name, description] of categories) {
            await pool.query(
                'INSERT INTO categories (name, description) VALUES ($1, $2) ON CONFLICT (name) DO NOTHING',
                [name, description]
            );
        }
        console.log('✅ Categories inserted\n');

        // Insert vendors
        console.log('2. Inserting vendors...');
        const vendors = [
            ['V1001', 'TechCorp Solutions', 'Rajesh Kumar', '9876543210', '<EMAIL>', 'Plot 123, Tech Park, Andheri East', 'Mumbai', 'Maharashtra', '30 Days', 4.5],
            ['V1002', 'AccessoryHub India', 'Priya Sharma', '9876543211', '<EMAIL>', 'Sector 18, Cyber City', 'Gurgaon', 'Haryana', '15 Days', 4.2],
            ['V1003', 'DisplayTech Systems', 'Amit Singh', '9876543212', '<EMAIL>', 'Electronic City, Phase 1', 'Bangalore', 'Karnataka', '45 Days', 4.7],
            ['V1004', 'ComponentKing Ltd', 'Neha Gupta', '9876543213', '<EMAIL>', 'Hinjewadi IT Park', 'Pune', 'Maharashtra', '30 Days', 4.3],
            ['V1005', 'StoragePro Systems', 'Vikram Patel', '9876543214', '<EMAIL>', 'OMR IT Corridor', 'Chennai', 'Tamil Nadu', '30 Days', 4.6]
        ];

        for (const vendor of vendors) {
            await pool.query(`
                INSERT INTO vendors (vendor_code, name, contact_person, phone, email, address, city, state, payment_terms, rating) 
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                ON CONFLICT (vendor_code) DO NOTHING
            `, vendor);
        }
        console.log('✅ Vendors inserted\n');

        // Insert customers
        console.log('3. Inserting customers...');
        const customers = [
            ['C2001', 'Tech Solutions Ltd', 'Suresh Reddy', '9123456789', '<EMAIL>', 'HITEC City, Madhapur', 'Hyderabad', 'Telangana', 'B2B', 500000.00],
            ['C2002', 'Gaming Zone', 'Anita Joshi', '9123456790', '<EMAIL>', 'Linking Road, Bandra', 'Mumbai', 'Maharashtra', 'B2C', 100000.00],
            ['C2003', 'Office Supplies Co', 'Rohit Mehta', '9123456791', '<EMAIL>', 'Connaught Place', 'New Delhi', 'Delhi', 'B2B', 300000.00],
            ['C2004', 'Digital World', 'Kavya Nair', '9123456792', '<EMAIL>', 'MG Road, Ernakulam', 'Kochi', 'Kerala', 'B2C', 150000.00],
            ['C2005', 'Smart Systems Pvt Ltd', 'Deepak Agarwal', '9123456793', '<EMAIL>', 'Malviya Nagar', 'Jaipur', 'Rajasthan', 'B2B', 400000.00],
            ['C2006', 'Cyber Cafe Network', 'Sanjay Kumar', '9123456794', '<EMAIL>', 'Park Street', 'Kolkata', 'West Bengal', 'B2B', 250000.00],
            ['C2007', 'Educational Institute', 'Dr. Meera Shah', '9123456795', '<EMAIL>', 'Koregaon Park', 'Pune', 'Maharashtra', 'B2B', 600000.00],
            ['C2008', 'Startup Hub', 'Arjun Kapoor', '9123456796', '<EMAIL>', 'Indiranagar', 'Bangalore', 'Karnataka', 'B2B', 350000.00]
        ];

        for (const customer of customers) {
            await pool.query(`
                INSERT INTO customers (customer_code, name, contact_person, phone, email, address, city, state, customer_type, credit_limit) 
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                ON CONFLICT (customer_code) DO NOTHING
            `, customer);
        }
        console.log('✅ Customers inserted\n');

        // Insert products
        console.log('4. Inserting products...');
        const products = [
            ['P3001', 'P3001', 'Gaming Laptop', 'High-performance gaming laptop with RTX graphics', 'Electronics', 45000.00, 52000.00, 'TechCorp Solutions', 5, 50, 12],
            ['P3002', 'P3002', 'Wireless Mouse', 'Ergonomic wireless mouse with RGB lighting', 'Accessories', 1200.00, 1440.00, 'AccessoryHub India', 20, 100, 45],
            ['P3003', 'P3003', 'Mechanical Keyboard', 'RGB mechanical keyboard with blue switches', 'Accessories', 3500.00, 4200.00, 'AccessoryHub India', 15, 80, 23],
            ['P3004', 'P3004', '4K Monitor', '27-inch 4K IPS monitor with HDR support', 'Electronics', 25000.00, 30000.00, 'DisplayTech Systems', 8, 40, 6],
            ['P3005', 'P3005', 'Webcam HD', '1080p HD webcam with auto-focus', 'Electronics', 4500.00, 5400.00, 'TechCorp Solutions', 12, 60, 3],
            ['P3006', 'P3006', 'Bluetooth Speaker', 'Portable Bluetooth speaker with bass boost', 'Electronics', 2800.00, 3360.00, 'AccessoryHub India', 10, 50, 18],
            ['P3007', 'P3007', 'External SSD 1TB', 'High-speed external SSD with USB 3.2', 'Storage', 8500.00, 10200.00, 'StoragePro Systems', 15, 75, 28],
            ['P3008', 'P3008', 'Graphics Card', 'RTX 4060 Ti graphics card for gaming', 'Components', 35000.00, 42000.00, 'ComponentKing Ltd', 6, 30, 4],
            ['P3009', 'P3009', 'Processor Intel i7', 'Intel Core i7 12th gen processor', 'Components', 28000.00, 33600.00, 'ComponentKing Ltd', 8, 40, 2],
            ['P3010', 'P3010', 'RAM 16GB DDR4', '16GB DDR4 3200MHz memory module', 'Components', 6500.00, 7800.00, 'ComponentKing Ltd', 12, 60, 35]
        ];

        for (const product of products) {
            // Get category and vendor IDs
            const categoryResult = await pool.query('SELECT id FROM categories WHERE name = $1', [product[4]]);
            const vendorResult = await pool.query('SELECT id FROM vendors WHERE name = $1', [product[7]]);
            
            if (categoryResult.rows.length > 0 && vendorResult.rows.length > 0) {
                await pool.query(`
                    INSERT INTO products (product_code, hsn_code, name, description, category_id, cost_price, selling_price, vendor_id, reorder_level, max_stock_level, current_stock) 
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
                    ON CONFLICT (product_code) DO NOTHING
                `, [
                    product[0], product[1], product[2], product[3], 
                    categoryResult.rows[0].id, product[5], product[6], 
                    vendorResult.rows[0].id, product[8], product[9], product[10]
                ]);
            }
        }
        console.log('✅ Products inserted\n');

        // Insert purchases
        console.log('5. Inserting purchases...');
        const purchases = [
            ['PUR4001', '2024-01-15', 'TechCorp Solutions', 'Gaming Laptop', 20, 45000.00, 900000.00, 162000.00, 1062000.00, 'Received', 'INV-TC-001'],
            ['PUR4002', '2024-01-16', 'AccessoryHub India', 'Wireless Mouse', 50, 1200.00, 60000.00, 10800.00, 70800.00, 'Received', 'INV-AH-002'],
            ['PUR4003', '2024-01-17', 'ComponentKing Ltd', 'Graphics Card', 10, 35000.00, 350000.00, 63000.00, 413000.00, 'Pending', 'INV-CK-003'],
            ['PUR4004', '2024-01-18', 'DisplayTech Systems', '4K Monitor', 15, 25000.00, 375000.00, 67500.00, 442500.00, 'Received', 'INV-DT-004'],
            ['PUR4005', '2024-01-19', 'StoragePro Systems', 'External SSD 1TB', 30, 8500.00, 255000.00, 45900.00, 300900.00, 'Received', 'INV-SP-005']
        ];

        for (const purchase of purchases) {
            const vendorResult = await pool.query('SELECT id FROM vendors WHERE name = $1', [purchase[2]]);
            const productResult = await pool.query('SELECT id FROM products WHERE name = $1', [purchase[3]]);
            
            if (vendorResult.rows.length > 0 && productResult.rows.length > 0) {
                await pool.query(`
                    INSERT INTO purchases (purchase_code, purchase_date, vendor_id, product_id, quantity, unit_cost, total_amount, tax_amount, final_amount, status, invoice_number, created_by) 
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
                    ON CONFLICT (purchase_code) DO NOTHING
                `, [
                    purchase[0], purchase[1], vendorResult.rows[0].id, productResult.rows[0].id,
                    purchase[4], purchase[5], purchase[6], purchase[7], purchase[8], purchase[9], purchase[10], 'admin'
                ]);
            }
        }
        console.log('✅ Purchases inserted\n');

        // Insert sales
        console.log('6. Inserting sales...');
        const sales = [
            ['SAL5001', '2024-01-20', 'Tech Solutions Ltd', 'Gaming Laptop', 5, 52000.00, 260000.00, 46800.00, 306800.00, 'Paid', 'Bank Transfer', '2024-01-20', 'INV-OUT-001'],
            ['SAL5002', '2024-01-21', 'Gaming Zone', 'Wireless Mouse', 10, 1440.00, 14400.00, 2592.00, 16992.00, 'Paid', 'Cash', '2024-01-21', 'INV-OUT-002'],
            ['SAL5003', '2024-01-22', 'Office Supplies Co', 'Mechanical Keyboard', 15, 4200.00, 63000.00, 11340.00, 74340.00, 'Pending', 'Credit', null, 'INV-OUT-003'],
            ['SAL5004', '2024-01-23', 'Digital World', '4K Monitor', 3, 30000.00, 90000.00, 16200.00, 106200.00, 'Paid', 'UPI', '2024-01-23', 'INV-OUT-004'],
            ['SAL5005', '2024-01-24', 'Smart Systems Pvt Ltd', 'Graphics Card', 2, 42000.00, 84000.00, 15120.00, 99120.00, 'Partial', 'Bank Transfer', '2024-01-24', 'INV-OUT-005']
        ];

        for (const sale of sales) {
            const customerResult = await pool.query('SELECT id FROM customers WHERE name = $1', [sale[2]]);
            const productResult = await pool.query('SELECT id FROM products WHERE name = $1', [sale[3]]);
            
            if (customerResult.rows.length > 0 && productResult.rows.length > 0) {
                await pool.query(`
                    INSERT INTO sales (sale_code, sale_date, customer_id, product_id, quantity, unit_price, total_amount, tax_amount, final_amount, payment_status, payment_method, payment_date, invoice_number, created_by) 
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
                    ON CONFLICT (sale_code) DO NOTHING
                `, [
                    sale[0], sale[1], customerResult.rows[0].id, productResult.rows[0].id,
                    sale[4], sale[5], sale[6], sale[7], sale[8], sale[9], sale[10], sale[11], sale[12], 'admin'
                ]);
            }
        }
        console.log('✅ Sales inserted\n');

        // Update sequences
        console.log('7. Updating sequences...');
        await pool.query("SELECT setval('vendor_code_seq', 1005)");
        await pool.query("SELECT setval('customer_code_seq', 2008)");
        await pool.query("SELECT setval('product_code_seq', 3010)");
        await pool.query("SELECT setval('purchase_code_seq', 4005)");
        await pool.query("SELECT setval('sale_code_seq', 5005)");
        console.log('✅ Sequences updated\n');

        // Verify data
        console.log('8. Verifying data...');
        const verificationQueries = [
            { name: 'Categories', query: 'SELECT COUNT(*) FROM categories' },
            { name: 'Vendors', query: 'SELECT COUNT(*) FROM vendors' },
            { name: 'Customers', query: 'SELECT COUNT(*) FROM customers' },
            { name: 'Products', query: 'SELECT COUNT(*) FROM products' },
            { name: 'Purchases', query: 'SELECT COUNT(*) FROM purchases' },
            { name: 'Sales', query: 'SELECT COUNT(*) FROM sales' }
        ];

        console.log('📊 Data Summary:');
        for (const { name, query } of verificationQueries) {
            const result = await pool.query(query);
            const count = result.rows[0].count;
            console.log(`   • ${name}: ${count} records`);
        }

        // Show critical stock alerts
        console.log('\n🚨 Stock Status:');
        const stockResult = await pool.query(`
            SELECT 
                product_code, name, current_stock, reorder_level,
                CASE 
                    WHEN current_stock <= reorder_level THEN 'Critical'
                    WHEN current_stock <= (reorder_level * 1.5) THEN 'Low Stock'
                    ELSE 'Adequate'
                END as status
            FROM products 
            WHERE is_active = TRUE
            ORDER BY current_stock
        `);

        stockResult.rows.forEach(item => {
            const icon = item.status === 'Critical' ? '🔴' : item.status === 'Low Stock' ? '🟡' : '🟢';
            console.log(`   ${icon} ${item.name} (${item.product_code}): ${item.current_stock} units - ${item.status}`);
        });

        console.log('\n🎉 Sample data insertion complete!');
        return true;

    } catch (error) {
        console.error('❌ Sample data insertion failed:', error.message);
        return false;
    }
}

// Run insertion
insertSampleData()
    .then(success => {
        if (success) {
            console.log('\n✅ Database is ready!');
            console.log('   Start server: npm run start:postgresql');
            console.log('   Open app: http://localhost:3000');
        }
        pool.end();
    })
    .catch(error => {
        console.error('❌ Insertion failed:', error);
        pool.end();
        process.exit(1);
    });

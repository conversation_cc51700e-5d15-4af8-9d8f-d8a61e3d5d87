// Complete Database Setup Script for All 14 Tables
const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function setupCompleteDatabase() {
    console.log('🚀 Setting up Complete Inventory Management Database...\n');

    try {
        // Test connection
        await pool.query('SELECT 1');
        console.log('✅ Connected to PostgreSQL\n');

        // Read and execute schema
        console.log('📋 Creating all 14 tables from schema...');
        const schemaPath = path.join(__dirname, 'database', 'schema.sql');
        const schema = await fs.readFile(schemaPath, 'utf8');
        
        // Split schema into individual statements and execute
        const statements = schema.split(';').filter(stmt => stmt.trim().length > 0);
        
        for (const statement of statements) {
            if (statement.trim()) {
                try {
                    await pool.query(statement);
                } catch (error) {
                    // Skip errors for existing objects
                    if (!error.message.includes('already exists')) {
                        console.warn('Warning:', error.message);
                    }
                }
            }
        }

        console.log('✅ All 14 tables created successfully!\n');

        // Insert sample data for all tables
        console.log('📊 Inserting sample data...\n');

        // 1. Sample Vendors
        await pool.query(`
            INSERT INTO vendor (company, vendor_code, vendor_name, address1, city, state, phone, mobile, email, gst, pan, active, created_by)
            VALUES
            (1, 1001, 'ABC Suppliers Ltd', '123 Industrial Area', 'Mumbai', 'Maharashtra', 2212345678, 9876543210, '<EMAIL>', '27**********1Z5', '**********', true, 'ADMIN'),
            (1, 1002, 'XYZ Trading Co', '456 Market Street', 'Delhi', 'Delhi', 1123456789, 9876543211, '<EMAIL>', '07**********2H6', '**********', true, 'ADMIN'),
            (1, 1003, 'PQR Industries', '789 Tech Park', 'Bangalore', 'Karnataka', 8012345678, 9876543212, '<EMAIL>', '29**********3J7', '**********', true, 'ADMIN')
            ON CONFLICT (company, vendor_code) DO NOTHING
        `);

        // 2. Sample Customers
        await pool.query(`
            INSERT INTO customer (company, customer_code, customer_name, address1, city, state, phone, mobile, email, gst, pan, active, created_by)
            VALUES
            (1, 2001, 'Tech Solutions Ltd', '100 Business Park', 'Pune', 'Maharashtra', 2087654321, 9123456789, '<EMAIL>', '27**********1L5', '**********', true, 'ADMIN'),
            (1, 2002, 'Global Enterprises', '200 Corporate Tower', 'Chennai', 'Tamil Nadu', 4487654321, 9123456790, '<EMAIL>', '33**********2N6', '**********', true, 'ADMIN'),
            (1, 2003, 'Smart Systems Ltd', '300 IT Hub', 'Hyderabad', 'Telangana', 4087654321, 9123456791, '<EMAIL>', '36**********3P7', '**********', true, 'ADMIN')
            ON CONFLICT (company, customer_code) DO NOTHING
        `);

        // 3. Sample Units
        await pool.query(`
            INSERT INTO unit (company, unit_id, unit_desc, active, created_by)
            VALUES 
            (1, 1, 'Pieces', true, 'ADMIN'),
            (1, 2, 'Kilograms', true, 'ADMIN'),
            (1, 3, 'Meters', true, 'ADMIN'),
            (1, 4, 'Liters', true, 'ADMIN'),
            (1, 5, 'Sets', true, 'ADMIN')
            ON CONFLICT (company, unit_id) DO NOTHING
        `);

        // 4. Sample Taxes
        await pool.query(`
            INSERT INTO tax (company, tax_id, tax_desc, tax_percent, active, created_by)
            VALUES 
            (1, 1, 'GST 5%', 5.00, true, 'ADMIN'),
            (1, 2, 'GST 12%', 12.00, true, 'ADMIN'),
            (1, 3, 'GST 18%', 18.00, true, 'ADMIN'),
            (1, 4, 'GST 28%', 28.00, true, 'ADMIN'),
            (1, 5, 'IGST 18%', 18.00, true, 'ADMIN')
            ON CONFLICT (company, tax_id) DO NOTHING
        `);

        // 5. Sample Parts
        await pool.query(`
            INSERT INTO part (company, part_num, part_desc, part_type, part_category, unit_id, pur_price, sell_price, hsn_code, tax_id, tax_percent, active, created_by)
            VALUES 
            (1, 3001, 'Steel Rod 12mm', 'Raw Material', 'Construction', 1, 45.50, 55.00, '72142090', 3, 18.00, true, 'ADMIN'),
            (1, 3002, 'Cement Bag 50kg', 'Raw Material', 'Construction', 2, 350.00, 420.00, '25232900', 2, 12.00, true, 'ADMIN'),
            (1, 3003, 'Electric Motor 1HP', 'Finished Goods', 'Electrical', 1, 2500.00, 3200.00, '85015100', 3, 18.00, true, 'ADMIN'),
            (1, 3004, 'Hydraulic Pump', 'Finished Goods', 'Machinery', 1, 15000.00, 18500.00, '84137090', 3, 18.00, true, 'ADMIN'),
            (1, 3005, 'Bearing 6205', 'Spare Parts', 'Mechanical', 1, 125.00, 165.00, '84821000', 3, 18.00, true, 'ADMIN')
            ON CONFLICT (company, part_num) DO NOTHING
        `);

        // 6. Sample Users
        await pool.query(`
            INSERT INTO "user" (company, user_id, user_key, active, created_by)
            VALUES 
            (1, 'ADMIN', 'admin123', true, 'SYSTEM'),
            (1, 'MANAGER1', 'mgr123', true, 'ADMIN'),
            (1, 'USER1', 'user123', true, 'ADMIN'),
            (1, 'VIEWER1', 'view123', true, 'ADMIN')
            ON CONFLICT (company, user_id) DO NOTHING
        `);

        // 7. Sample User Roles
        await pool.query(`
            INSERT INTO user_role (company, role_id, role_desc, active, created_by)
            VALUES 
            (1, 1, 'Administrator', true, 'SYSTEM'),
            (1, 2, 'Manager', true, 'ADMIN'),
            (1, 3, 'User', true, 'ADMIN'),
            (1, 4, 'Viewer', true, 'ADMIN')
            ON CONFLICT (company, role_id) DO NOTHING
        `);

        // 8. Sample Indent Requests
        await pool.query(`
            INSERT INTO indent_request (company, indent_id, indent_date, indent_by, active, created_by)
            VALUES 
            (1, 3001, CURRENT_DATE, 'USER1', true, 'USER1'),
            (1, 3002, CURRENT_DATE - INTERVAL '1 day', 'MANAGER1', true, 'MANAGER1'),
            (1, 3003, CURRENT_DATE - INTERVAL '2 days', 'USER1', true, 'USER1')
            ON CONFLICT (company, indent_id) DO NOTHING
        `);

        // 9. Sample Purchase Orders
        await pool.query(`
            INSERT INTO purchase_order (company, po_no, po_date, vendor, indent_id, active, created_by)
            VALUES 
            (1, 4001, CURRENT_DATE, 1001, 3001, true, 'MANAGER1'),
            (1, 4002, CURRENT_DATE - INTERVAL '1 day', 1002, 3002, true, 'MANAGER1'),
            (1, 4003, CURRENT_DATE - INTERVAL '3 days', 1003, 3003, true, 'MANAGER1')
            ON CONFLICT (company, po_no) DO NOTHING
        `);

        // 10. Sample Goods Receipts
        await pool.query(`
            INSERT INTO goods_receipt (company, grn_no, grn_dt, vendor, po_no, active, created_by)
            VALUES 
            (1, 5001, CURRENT_DATE, 1001, 4001, true, 'USER1'),
            (1, 5002, CURRENT_DATE - INTERVAL '2 days', 1002, 4002, true, 'USER1')
            ON CONFLICT (company, grn_no) DO NOTHING
        `);

        // 11. Sample Sales Invoices
        await pool.query(`
            INSERT INTO sales_invoice (company, inv_no, inv_dt, customer, po_no, active, created_by)
            VALUES 
            (1, 6001, CURRENT_DATE, 2001, 4001, true, 'USER1'),
            (1, 6002, CURRENT_DATE - INTERVAL '1 day', 2002, 4002, true, 'USER1'),
            (1, 6003, CURRENT_DATE - INTERVAL '3 days', 2003, 4003, true, 'USER1')
            ON CONFLICT (company, inv_no) DO NOTHING
        `);

        // 12. Sample Accounts Payable
        await pool.query(`
            INSERT INTO accounts_payable (company, ap_id, ap_date, vendor_code, vendor_invoice, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by)
            VALUES 
            (1, 7001, CURRENT_DATE, 1001, 'INV-001', CURRENT_DATE + INTERVAL '30 days', 4001, 50000.00, 25000.00, 25000.00, true, 'ADMIN'),
            (1, 7002, CURRENT_DATE - INTERVAL '5 days', 1002, 'INV-002', CURRENT_DATE + INTERVAL '25 days', 4002, 75000.00, 0.00, 75000.00, true, 'ADMIN'),
            (1, 7003, CURRENT_DATE - INTERVAL '10 days', 1003, 'INV-003', CURRENT_DATE + INTERVAL '20 days', 4003, 120000.00, 60000.00, 60000.00, true, 'ADMIN')
            ON CONFLICT (company, ap_id) DO NOTHING
        `);

        // 13. Sample Accounts Receivable
        await pool.query(`
            INSERT INTO accounts_receivable (company, ar_id, ar_date, customer_code, invoice_number, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by)
            VALUES 
            (1, 8001, CURRENT_DATE, 2001, '6001', CURRENT_DATE + INTERVAL '30 days', 'PO-2001-001', 65000.00, 32500.00, 32500.00, true, 'ADMIN'),
            (1, 8002, CURRENT_DATE - INTERVAL '3 days', 2002, '6002', CURRENT_DATE + INTERVAL '27 days', 'PO-2002-001', 85000.00, 0.00, 85000.00, true, 'ADMIN'),
            (1, 8003, CURRENT_DATE - INTERVAL '7 days', 2003, '6003', CURRENT_DATE + INTERVAL '23 days', 'PO-2003-001', 145000.00, 72500.00, 72500.00, true, 'ADMIN')
            ON CONFLICT (company, ar_id) DO NOTHING
        `);

        // 14. Sample Stock Updates
        await pool.query(`
            INSERT INTO stock_update (company, update_id, update_date, type, qty, created_by)
            VALUES 
            (1, 9001, CURRENT_DATE, 'IN', 100, 'USER1'),
            (1, 9002, CURRENT_DATE, 'OUT', 25, 'USER1'),
            (1, 9003, CURRENT_DATE - INTERVAL '1 day', 'IN', 200, 'USER1'),
            (1, 9004, CURRENT_DATE - INTERVAL '1 day', 'ADJ', -5, 'MANAGER1'),
            (1, 9005, CURRENT_DATE - INTERVAL '2 days', 'OUT', 50, 'USER1')
            ON CONFLICT (company, update_id) DO NOTHING
        `);

        console.log('✅ Sample data inserted successfully!\n');

        // Verify table creation
        console.log('🔍 Verifying all 14 tables...\n');
        
        const tables = [
            'vendor', 'customer', 'part', 'unit', '"user"', 'user_role', 'tax',
            'indent_request', 'purchase_order', 'goods_receipt', 'sales_invoice',
            'accounts_payable', 'accounts_receivable', 'stock_update'
        ];

        for (const table of tables) {
            try {
                const result = await pool.query(`SELECT COUNT(*) FROM ${table}`);
                const count = result.rows[0].count;
                console.log(`✅ ${table.replace('"', '').replace('"', '').toUpperCase()}: ${count} records`);
            } catch (error) {
                console.log(`❌ ${table.toUpperCase()}: Error - ${error.message}`);
            }
        }

        console.log('\n🎉 Complete Inventory Management Database Setup Completed!');
        console.log('\n📋 Summary:');
        console.log('   • All 14 tables created successfully');
        console.log('   • Sample data inserted for testing');
        console.log('   • Multi-company support enabled');
        console.log('   • Ready for production use');
        console.log('\n🚀 You can now start the server with: node server-complete.js');

    } catch (error) {
        console.error('❌ Setup failed:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Run setup if called directly
if (require.main === module) {
    setupCompleteDatabase();
}

module.exports = { setupCompleteDatabase };

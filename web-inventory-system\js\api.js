// API Layer for Backend Communication
class InventoryAPI {
    constructor() {
        this.baseURL = '/api'; // This would be your actual API endpoint
        this.isOnline = navigator.onLine;
        this.setupOfflineHandling();
    }

    setupOfflineHandling() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
        });
    }

    // Generic API request method
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            if (!this.isOnline) {
                throw new Error('No internet connection');
            }

            const response = await fetch(url, config);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            
            // Fallback to local data when API is unavailable
            return this.handleOfflineRequest(endpoint, options);
        }
    }

    handleOfflineRequest(endpoint, options) {
        // This method handles requests when offline
        // For now, it returns local data
        console.log('Using offline data for:', endpoint);
        
        switch (endpoint) {
            case '/products':
                return { data: dataManager.getProducts(), success: true };
            case '/vendors':
                return { data: dataManager.getVendors(), success: true };
            case '/customers':
                return { data: dataManager.getCustomers(), success: true };
            case '/purchases':
                return { data: dataManager.getPurchases(), success: true };
            case '/sales':
                return { data: dataManager.getSales(), success: true };
            case '/inventory':
                return { data: dataManager.getInventory(), success: true };
            case '/analytics':
                return { data: dataManager.getAnalytics(), success: true };
            default:
                return { data: null, success: false, error: 'Endpoint not found' };
        }
    }

    // Product API methods
    async getProducts() {
        return await this.request('/products');
    }

    async getProduct(id) {
        return await this.request(`/products/${id}`);
    }

    async createProduct(productData) {
        return await this.request('/products', {
            method: 'POST',
            body: JSON.stringify(productData)
        });
    }

    async updateProduct(id, productData) {
        return await this.request(`/products/${id}`, {
            method: 'PUT',
            body: JSON.stringify(productData)
        });
    }

    async deleteProduct(id) {
        return await this.request(`/products/${id}`, {
            method: 'DELETE'
        });
    }

    // Vendor API methods
    async getVendors() {
        return await this.request('/vendors');
    }

    async createVendor(vendorData) {
        return await this.request('/vendors', {
            method: 'POST',
            body: JSON.stringify(vendorData)
        });
    }

    async updateVendor(id, vendorData) {
        return await this.request(`/vendors/${id}`, {
            method: 'PUT',
            body: JSON.stringify(vendorData)
        });
    }

    // Customer API methods
    async getCustomers() {
        return await this.request('/customers');
    }

    async createCustomer(customerData) {
        return await this.request('/customers', {
            method: 'POST',
            body: JSON.stringify(customerData)
        });
    }

    async updateCustomer(id, customerData) {
        return await this.request(`/customers/${id}`, {
            method: 'PUT',
            body: JSON.stringify(customerData)
        });
    }

    // Purchase API methods
    async getPurchases() {
        return await this.request('/purchases');
    }

    async createPurchase(purchaseData) {
        return await this.request('/purchases', {
            method: 'POST',
            body: JSON.stringify(purchaseData)
        });
    }

    async updatePurchaseStatus(id, status) {
        return await this.request(`/purchases/${id}/status`, {
            method: 'PATCH',
            body: JSON.stringify({ status })
        });
    }

    // Sales API methods
    async getSales() {
        return await this.request('/sales');
    }

    async createSale(saleData) {
        return await this.request('/sales', {
            method: 'POST',
            body: JSON.stringify(saleData)
        });
    }

    async updatePaymentStatus(id, paymentStatus) {
        return await this.request(`/sales/${id}/payment`, {
            method: 'PATCH',
            body: JSON.stringify({ paymentStatus })
        });
    }

    // Inventory API methods
    async getInventory() {
        return await this.request('/inventory');
    }

    async updateStock(productId, quantity, type = 'adjustment') {
        return await this.request(`/inventory/${productId}/stock`, {
            method: 'PATCH',
            body: JSON.stringify({ quantity, type })
        });
    }

    async getStockAlerts() {
        return await this.request('/inventory/alerts');
    }

    // Analytics API methods
    async getAnalytics(dateRange = null) {
        const params = dateRange ? `?start=${dateRange.start}&end=${dateRange.end}` : '';
        return await this.request(`/analytics${params}`);
    }

    async getSalesAnalytics(period = 'month') {
        return await this.request(`/analytics/sales?period=${period}`);
    }

    async getInventoryAnalytics() {
        return await this.request('/analytics/inventory');
    }

    async getCustomerAnalytics() {
        return await this.request('/analytics/customers');
    }

    async getVendorAnalytics() {
        return await this.request('/analytics/vendors');
    }

    // Search API methods
    async searchProducts(query) {
        return await this.request(`/search/products?q=${encodeURIComponent(query)}`);
    }

    async searchCustomers(query) {
        return await this.request(`/search/customers?q=${encodeURIComponent(query)}`);
    }

    async searchVendors(query) {
        return await this.request(`/search/vendors?q=${encodeURIComponent(query)}`);
    }

    // Reports API methods
    async generateReport(type, params = {}) {
        return await this.request('/reports/generate', {
            method: 'POST',
            body: JSON.stringify({ type, params })
        });
    }

    async getReportStatus(reportId) {
        return await this.request(`/reports/${reportId}/status`);
    }

    async downloadReport(reportId) {
        const response = await fetch(`${this.baseURL}/reports/${reportId}/download`);
        return response.blob();
    }

    // Backup and sync methods
    async backupData() {
        const allData = {
            products: dataManager.getProducts(),
            vendors: dataManager.getVendors(),
            customers: dataManager.getCustomers(),
            purchases: dataManager.getPurchases(),
            sales: dataManager.getSales(),
            timestamp: new Date().toISOString()
        };

        return await this.request('/backup', {
            method: 'POST',
            body: JSON.stringify(allData)
        });
    }

    async restoreData(backupId) {
        return await this.request(`/backup/${backupId}/restore`, {
            method: 'POST'
        });
    }

    async syncOfflineData() {
        // This method would sync any offline changes when connection is restored
        const offlineChanges = this.getOfflineChanges();
        
        if (offlineChanges.length > 0) {
            try {
                const result = await this.request('/sync', {
                    method: 'POST',
                    body: JSON.stringify({ changes: offlineChanges })
                });

                if (result.success) {
                    this.clearOfflineChanges();
                    console.log('Offline data synced successfully');
                }
            } catch (error) {
                console.error('Failed to sync offline data:', error);
            }
        }
    }

    getOfflineChanges() {
        const changes = localStorage.getItem('offlineChanges');
        return changes ? JSON.parse(changes) : [];
    }

    addOfflineChange(change) {
        const changes = this.getOfflineChanges();
        changes.push({
            ...change,
            timestamp: new Date().toISOString()
        });
        localStorage.setItem('offlineChanges', JSON.stringify(changes));
    }

    clearOfflineChanges() {
        localStorage.removeItem('offlineChanges');
    }

    // Utility methods
    async healthCheck() {
        try {
            const result = await this.request('/health');
            return result.status === 'ok';
        } catch (error) {
            return false;
        }
    }

    async getSystemInfo() {
        return await this.request('/system/info');
    }

    // Real-time updates (WebSocket simulation)
    setupRealTimeUpdates() {
        // In a real application, this would establish WebSocket connection
        // For now, we'll simulate with periodic polling
        setInterval(async () => {
            if (this.isOnline) {
                try {
                    const alerts = await this.getStockAlerts();
                    if (alerts.data && alerts.data.length > 0) {
                        this.handleRealTimeAlerts(alerts.data);
                    }
                } catch (error) {
                    console.error('Failed to fetch real-time updates:', error);
                }
            }
        }, 30000); // Check every 30 seconds
    }

    handleRealTimeAlerts(alerts) {
        // Update notification count
        const notificationCount = document.querySelector('.notification-count');
        if (notificationCount) {
            notificationCount.textContent = alerts.length;
        }

        // Show toast notifications for critical alerts
        alerts.forEach(alert => {
            if (alert.level === 'critical') {
                this.showToastNotification(alert.message, 'error');
            }
        });
    }

    showToastNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <span>${message}</span>
                <button class="toast-close">&times;</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);

        // Manual close
        toast.querySelector('.toast-close').addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }
}

// Initialize API
const api = new InventoryAPI();

// Setup real-time updates
api.setupRealTimeUpdates();

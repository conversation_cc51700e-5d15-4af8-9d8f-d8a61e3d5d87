/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: 260px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transition: all 0.3s ease;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.sidebar-header i {
    margin-right: 10px;
    color: #ffd700;
}

.nav-menu {
    list-style: none;
    padding: 20px 0;
}

.nav-item {
    margin: 5px 0;
}

.nav-item a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-item a:hover,
.nav-item.active a {
    background: rgba(255,255,255,0.1);
    color: white;
    border-left-color: #ffd700;
}

.nav-item i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header */
.header {
    background: white;
    padding: 0 30px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    margin-right: 20px;
    cursor: pointer;
    color: #666;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-box {
    position: relative;
}

.search-box input {
    padding: 10px 40px 10px 15px;
    border: 1px solid #ddd;
    border-radius: 25px;
    width: 300px;
    outline: none;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
}

.notification-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* Content Container */
.content-container {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

/* Screen Management */
.screen {
    display: none;
}

.screen.active {
    display: block;
}

/* Dashboard Styles */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 30px;
    height: 100%;
}

.kpi-cards {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.kpi-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.kpi-card:hover {
    transform: translateY(-5px);
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
}

.kpi-card:nth-child(1) .kpi-icon { background: linear-gradient(135deg, #667eea, #764ba2); }
.kpi-card:nth-child(2) .kpi-icon { background: linear-gradient(135deg, #f093fb, #f5576c); }
.kpi-card:nth-child(3) .kpi-icon { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.kpi-card:nth-child(4) .kpi-icon { background: linear-gradient(135deg, #43e97b, #38f9d7); }

.kpi-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.kpi-content p {
    color: #666;
    font-size: 0.9rem;
}

.charts-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.chart-container {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.chart-container h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 1.1rem;
}

.alerts-section,
.recent-activity {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.alerts-section h3,
.recent-activity h3 {
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.alerts-list,
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.alert-item {
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 10px;
    border-left: 4px solid #ff4757;
    background: #fff5f5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-item.warning {
    border-left-color: #ffa502;
    background: #fffbf0;
}

.activity-item {
    padding: 15px 0;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

/* Screen Header */
.screen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.screen-header h2 {
    color: #333;
    font-size: 1.8rem;
    font-weight: 600;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-danger {
    background: #ff4757;
    color: white;
}

.btn-success {
    background: #2ed573;
    color: white;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.filters-section input,
.filters-section select {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    outline: none;
    transition: border-color 0.3s ease;
}

.filters-section input:focus,
.filters-section select:focus {
    border-color: #667eea;
}

/* Table Styles */
.table-container {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #dee2e6;
}

.data-table td {
    padding: 15px;
    border-bottom: 1px solid #f1f3f4;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-critical {
    background: #ffebee;
    color: #c62828;
}

.status-low {
    background: #fff3e0;
    color: #ef6c00;
}

.status-adequate {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-paid {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-pending {
    background: #fff3e0;
    color: #ef6c00;
}

/* Inventory Summary */
.inventory-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-top: 4px solid;
}

.summary-card.critical {
    border-top-color: #ff4757;
}

.summary-card.low {
    border-top-color: #ffa502;
}

.summary-card.good {
    border-top-color: #2ed573;
}

.summary-card h4 {
    color: #666;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.summary-card span {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.analytics-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.analytics-card h3 {
    margin-bottom: 20px;
    color: #333;
}

/* Entry Options */
.entry-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.entry-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.entry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.entry-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.entry-card h3 {
    color: #333;
    margin-bottom: 10px;
}

.entry-card p {
    color: #666;
    font-size: 0.9rem;
}

/* Date Range Picker */
.date-range-picker {
    display: flex;
    gap: 10px;
    align-items: center;
}

.date-range-picker input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    outline: none;
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 0;
        overflow: hidden;
    }
    
    .sidebar.open {
        width: 260px;
    }
    
    .content-container {
        padding: 20px;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-section {
        grid-template-columns: 1fr;
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
    
    .kpi-cards {
        grid-template-columns: 1fr;
    }
    
    .search-box input {
        width: 200px;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-header h3 {
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    outline: none;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #667eea;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
}

/* Action Buttons */
.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    margin: 0 2px;
}

.action-btn.edit {
    background: #17a2b8;
    color: white;
}

.action-btn.delete {
    background: #dc3545;
    color: white;
}

.action-btn.view {
    background: #28a745;
    color: white;
}

.action-btn:hover {
    opacity: 0.8;
}

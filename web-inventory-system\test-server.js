// Test Server Startup
const { Pool } = require('pg');
require('dotenv').config();

console.log('🔍 Testing server startup...\n');

// Test database connection
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function testConnection() {
    try {
        console.log('📡 Testing database connection...');
        const result = await pool.query('SELECT COUNT(*) as count FROM products');
        console.log(`✅ Database connected! Found ${result.rows[0].count} products`);
        
        // Test a simple query
        const products = await pool.query('SELECT name, current_stock FROM products LIMIT 3');
        console.log('📦 Sample products:');
        products.rows.forEach(p => {
            console.log(`   • ${p.name}: ${p.current_stock} units`);
        });
        
        await pool.end();
        console.log('\n✅ Database test successful!');
        console.log('🚀 Ready to start the web server!');
        
    } catch (error) {
        console.error('❌ Database test failed:', error.message);
        await pool.end();
    }
}

testConnection();

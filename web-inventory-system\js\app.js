// Main Application JavaScript
class InventoryApp {
    constructor() {
        this.currentScreen = 'dashboard';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadDashboard();
        this.updateNotificationCount();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const screen = item.dataset.screen;
                this.switchScreen(screen);
            });
        });

        // Search functionality
        document.getElementById('global-search').addEventListener('input', (e) => {
            this.handleGlobalSearch(e.target.value);
        });

        // Product search and filter
        const productSearch = document.getElementById('product-search');
        if (productSearch) {
            productSearch.addEventListener('input', (e) => {
                this.filterProducts(e.target.value);
            });
        }

        const categoryFilter = document.getElementById('category-filter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterProductsByCategory(e.target.value);
            });
        }

        // Menu toggle for mobile
        document.querySelector('.menu-toggle').addEventListener('click', () => {
            document.querySelector('.sidebar').classList.toggle('open');
        });
    }

    switchScreen(screenName) {
        // Hide all screens
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });

        // Show selected screen
        document.getElementById(`${screenName}-screen`).classList.add('active');

        // Update navigation
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-screen="${screenName}"]`).classList.add('active');

        // Update page title
        const titles = {
            'dashboard': 'Dashboard',
            'products': 'Products Management',
            'vendors': 'Vendors Management',
            'customers': 'Customers Management',
            'purchase': 'Purchase Management',
            'sales': 'Sales Management',
            'inventory': 'Inventory Management',
            'analytics': 'Analytics & Reports',
            'new-entry': 'Quick Entry'
        };
        document.getElementById('page-title').textContent = titles[screenName];

        // Load screen data
        this.loadScreenData(screenName);
        this.currentScreen = screenName;
    }

    loadScreenData(screenName) {
        switch (screenName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'products':
                this.loadProducts();
                break;
            case 'vendors':
                this.loadVendors();
                break;
            case 'customers':
                this.loadCustomers();
                break;
            case 'purchase':
                this.loadPurchases();
                break;
            case 'sales':
                this.loadSales();
                break;
            case 'inventory':
                this.loadInventory();
                break;
            case 'analytics':
                this.loadAnalytics();
                break;
        }
    }

    loadDashboard() {
        const analytics = dataManager.getAnalytics();
        
        // Update KPI cards
        document.getElementById('total-products').textContent = analytics.totalProducts;
        document.getElementById('total-customers').textContent = analytics.totalCustomers;
        document.getElementById('total-vendors').textContent = analytics.totalVendors;
        document.getElementById('total-sales').textContent = `₹${analytics.totalSales.toLocaleString()}`;

        // Load stock alerts
        this.loadStockAlerts(analytics);

        // Load recent activity
        this.loadRecentActivity();

        // Load charts
        this.loadDashboardCharts();
    }

    loadStockAlerts(analytics) {
        const alertsContainer = document.getElementById('stock-alerts');
        alertsContainer.innerHTML = '';

        // Critical stock alerts
        analytics.criticalStockItems.forEach(item => {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert-item';
            alertDiv.innerHTML = `
                <div>
                    <strong>🚨 ${item.name}</strong>
                    <p>Only ${item.currentStock} units left (Reorder: ${item.reorderLevel})</p>
                </div>
                <button class="btn btn-sm btn-primary" onclick="contactVendor('${item.supplier}')">
                    Contact Vendor
                </button>
            `;
            alertsContainer.appendChild(alertDiv);
        });

        // Low stock alerts
        analytics.lowStockItems.forEach(item => {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert-item warning';
            alertDiv.innerHTML = `
                <div>
                    <strong>⚠️ ${item.name}</strong>
                    <p>${item.currentStock} units remaining</p>
                </div>
                <button class="btn btn-sm btn-secondary" onclick="planReorder('${item.id}')">
                    Plan Reorder
                </button>
            `;
            alertsContainer.appendChild(alertDiv);
        });

        if (analytics.criticalStockItems.length === 0 && analytics.lowStockItems.length === 0) {
            alertsContainer.innerHTML = '<p class="text-success">✅ All products have adequate stock levels</p>';
        }
    }

    loadRecentActivity() {
        const activityContainer = document.getElementById('recent-activity-list');
        const recentSales = dataManager.getSales().slice(-5).reverse();
        const recentPurchases = dataManager.getPurchases().slice(-3).reverse();

        activityContainer.innerHTML = '';

        // Recent sales
        recentSales.forEach(sale => {
            const activityDiv = document.createElement('div');
            activityDiv.className = 'activity-item';
            activityDiv.innerHTML = `
                <div>
                    <strong>Sale: ${sale.productName}</strong>
                    <p>Sold ${sale.quantity} units to ${sale.customerName}</p>
                </div>
                <div>
                    <span class="text-success">₹${sale.totalAmount.toLocaleString()}</span>
                    <small class="text-muted">${new Date(sale.date).toLocaleDateString()}</small>
                </div>
            `;
            activityContainer.appendChild(activityDiv);
        });

        // Recent purchases
        recentPurchases.forEach(purchase => {
            const activityDiv = document.createElement('div');
            activityDiv.className = 'activity-item';
            activityDiv.innerHTML = `
                <div>
                    <strong>Purchase: ${purchase.productName}</strong>
                    <p>Received ${purchase.quantity} units from ${purchase.vendor}</p>
                </div>
                <div>
                    <span class="text-primary">₹${purchase.totalAmount.toLocaleString()}</span>
                    <small class="text-muted">${new Date(purchase.date).toLocaleDateString()}</small>
                </div>
            `;
            activityContainer.appendChild(activityDiv);
        });
    }

    loadProducts() {
        const products = dataManager.getProducts();
        const tbody = document.querySelector('#products-table tbody');
        
        tbody.innerHTML = '';
        
        products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.hsnCode}</td>
                <td>${product.name}</td>
                <td>${product.category}</td>
                <td>₹${product.costPrice.toLocaleString()}</td>
                <td>₹${product.sellingPrice.toLocaleString()}</td>
                <td>${product.supplier}</td>
                <td>
                    <span class="status-badge ${product.currentStock <= product.reorderLevel ? 'status-critical' : 'status-adequate'}">
                        ${product.currentStock}
                    </span>
                </td>
                <td>
                    <button class="action-btn edit" onclick="editProduct('${product.id}')">Edit</button>
                    <button class="action-btn view" onclick="viewProduct('${product.id}')">View</button>
                    <button class="action-btn delete" onclick="deleteProduct('${product.id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    loadVendors() {
        const vendors = dataManager.getVendors();
        const tbody = document.querySelector('#vendors-table tbody');
        
        tbody.innerHTML = '';
        
        vendors.forEach(vendor => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${vendor.id}</td>
                <td>${vendor.name}</td>
                <td>${vendor.contactPerson}</td>
                <td>${vendor.phone}</td>
                <td>${vendor.email}</td>
                <td>${vendor.address}</td>
                <td>
                    <div class="rating">
                        ${'★'.repeat(Math.floor(vendor.rating))}${'☆'.repeat(5 - Math.floor(vendor.rating))}
                        ${vendor.rating}
                    </div>
                </td>
                <td>
                    <button class="action-btn edit" onclick="editVendor('${vendor.id}')">Edit</button>
                    <button class="action-btn view" onclick="viewVendor('${vendor.id}')">View</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    loadCustomers() {
        const customers = dataManager.getCustomers();
        const tbody = document.querySelector('#customers-table tbody');
        
        tbody.innerHTML = '';
        
        customers.forEach(customer => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.id}</td>
                <td>${customer.name}</td>
                <td>${customer.contactPerson}</td>
                <td>${customer.phone}</td>
                <td>${customer.email}</td>
                <td>
                    <span class="status-badge ${customer.type === 'B2B' ? 'status-adequate' : 'status-low'}">
                        ${customer.type}
                    </span>
                </td>
                <td>₹${customer.creditLimit.toLocaleString()}</td>
                <td>
                    <button class="action-btn edit" onclick="editCustomer('${customer.id}')">Edit</button>
                    <button class="action-btn view" onclick="viewCustomer('${customer.id}')">View</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    loadPurchases() {
        const purchases = dataManager.getPurchases();
        const tbody = document.querySelector('#purchase-table tbody');
        
        tbody.innerHTML = '';
        
        purchases.forEach(purchase => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${purchase.id}</td>
                <td>${new Date(purchase.date).toLocaleDateString()}</td>
                <td>${purchase.productName}</td>
                <td>${purchase.vendor}</td>
                <td>${purchase.quantity}</td>
                <td>₹${purchase.unitCost.toLocaleString()}</td>
                <td>₹${purchase.totalAmount.toLocaleString()}</td>
                <td>
                    <span class="status-badge ${purchase.status === 'Received' ? 'status-adequate' : 'status-pending'}">
                        ${purchase.status}
                    </span>
                </td>
                <td>
                    <button class="action-btn view" onclick="viewPurchase('${purchase.id}')">View</button>
                    ${purchase.status !== 'Received' ? `<button class="action-btn edit" onclick="updatePurchaseStatus('${purchase.id}')">Update</button>` : ''}
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    loadSales() {
        const sales = dataManager.getSales();
        const tbody = document.querySelector('#sales-table tbody');
        
        tbody.innerHTML = '';
        
        sales.forEach(sale => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${sale.id}</td>
                <td>${new Date(sale.date).toLocaleDateString()}</td>
                <td>${sale.customerName}</td>
                <td>${sale.productName}</td>
                <td>${sale.quantity}</td>
                <td>₹${sale.unitPrice.toLocaleString()}</td>
                <td>₹${sale.totalAmount.toLocaleString()}</td>
                <td>
                    <span class="status-badge ${sale.paymentStatus === 'Paid' ? 'status-paid' : 'status-pending'}">
                        ${sale.paymentStatus}
                    </span>
                </td>
                <td>
                    <button class="action-btn view" onclick="viewSale('${sale.id}')">View</button>
                    ${sale.paymentStatus !== 'Paid' ? `<button class="action-btn edit" onclick="updatePaymentStatus('${sale.id}')">Update</button>` : ''}
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    loadInventory() {
        const inventory = dataManager.getInventory();
        const tbody = document.querySelector('#inventory-table tbody');
        
        // Update summary cards
        const criticalCount = inventory.filter(item => item.status === 'Critical').length;
        const lowCount = inventory.filter(item => item.status === 'Low Stock').length;
        const adequateCount = inventory.filter(item => item.status === 'Adequate').length;
        
        document.getElementById('critical-count').textContent = criticalCount;
        document.getElementById('low-count').textContent = lowCount;
        document.getElementById('adequate-count').textContent = adequateCount;
        
        tbody.innerHTML = '';
        
        inventory.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.name}</td>
                <td>${item.currentStock}</td>
                <td>${item.reorderLevel}</td>
                <td>${item.maxStockLevel}</td>
                <td>₹${item.stockValue.toLocaleString()}</td>
                <td>
                    <span class="status-badge ${item.status === 'Critical' ? 'status-critical' : 
                        item.status === 'Low Stock' ? 'status-low' : 'status-adequate'}">
                        ${item.status}
                    </span>
                </td>
                <td>${new Date(item.lastUpdated).toLocaleString()}</td>
                <td>
                    ${item.status === 'Critical' ? 
                        `<button class="action-btn delete" onclick="contactVendor('${item.supplier}')">Reorder Now</button>` :
                        `<button class="action-btn edit" onclick="adjustStock('${item.id}')">Adjust</button>`
                    }
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    loadAnalytics() {
        // This will be implemented with charts
        this.loadAnalyticsCharts();
    }

    loadDashboardCharts() {
        // Sales trend chart
        const salesData = dataManager.getSales();
        const salesByDate = {};
        
        salesData.forEach(sale => {
            const date = sale.date;
            salesByDate[date] = (salesByDate[date] || 0) + sale.totalAmount;
        });

        const salesChart = new Chart(document.getElementById('salesChart'), {
            type: 'line',
            data: {
                labels: Object.keys(salesByDate).slice(-7),
                datasets: [{
                    label: 'Sales Amount',
                    data: Object.values(salesByDate).slice(-7),
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Stock levels chart
        const products = dataManager.getProducts().slice(0, 6);
        const stockChart = new Chart(document.getElementById('stockChart'), {
            type: 'bar',
            data: {
                labels: products.map(p => p.name.substring(0, 10) + '...'),
                datasets: [{
                    label: 'Current Stock',
                    data: products.map(p => p.currentStock),
                    backgroundColor: products.map(p => 
                        p.currentStock <= p.reorderLevel ? '#ff4757' : 
                        p.currentStock <= p.reorderLevel * 1.5 ? '#ffa502' : '#2ed573'
                    )
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    loadAnalyticsCharts() {
        // Implementation for analytics charts
        // This would include more detailed charts for the analytics screen
    }

    filterProducts(query) {
        const products = query ? dataManager.searchProducts(query) : dataManager.getProducts();
        this.renderProductsTable(products);
    }

    filterProductsByCategory(category) {
        const products = dataManager.filterProductsByCategory(category);
        this.renderProductsTable(products);
    }

    renderProductsTable(products) {
        const tbody = document.querySelector('#products-table tbody');
        tbody.innerHTML = '';
        
        products.forEach(product => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${product.hsnCode}</td>
                <td>${product.name}</td>
                <td>${product.category}</td>
                <td>₹${product.costPrice.toLocaleString()}</td>
                <td>₹${product.sellingPrice.toLocaleString()}</td>
                <td>${product.supplier}</td>
                <td>
                    <span class="status-badge ${product.currentStock <= product.reorderLevel ? 'status-critical' : 'status-adequate'}">
                        ${product.currentStock}
                    </span>
                </td>
                <td>
                    <button class="action-btn edit" onclick="editProduct('${product.id}')">Edit</button>
                    <button class="action-btn view" onclick="viewProduct('${product.id}')">View</button>
                    <button class="action-btn delete" onclick="deleteProduct('${product.id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    handleGlobalSearch(query) {
        if (!query) return;
        
        // Search across all data types
        const results = {
            products: dataManager.searchProducts(query),
            // Add more search functionality as needed
        };
        
        // Show search results or navigate to relevant screen
        console.log('Search results:', results);
    }

    updateNotificationCount() {
        const analytics = dataManager.getAnalytics();
        const totalAlerts = analytics.criticalStockCount + analytics.lowStockCount;
        document.querySelector('.notification-count').textContent = totalAlerts;
    }

    showLoading() {
        document.getElementById('loading-spinner').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-spinner').style.display = 'none';
    }
}

// Global functions for button actions
function contactVendor(vendorName) {
    const vendor = dataManager.getVendors().find(v => v.name === vendorName);
    if (vendor) {
        alert(`Contact ${vendor.contactPerson} at ${vendor.phone} (${vendor.name})`);
    }
}

function planReorder(productId) {
    const product = dataManager.getProduct(productId);
    if (product) {
        const reorderQuantity = product.maxStockLevel - product.currentStock;
        alert(`Plan to reorder ${reorderQuantity} units of ${product.name}`);
    }
}

function refreshInventory() {
    app.loadInventory();
    app.updateNotificationCount();
}

function updateAnalytics() {
    app.loadAnalytics();
}

// Placeholder functions for CRUD operations
function editProduct(id) { console.log('Edit product:', id); }
function viewProduct(id) { console.log('View product:', id); }
function deleteProduct(id) { 
    if (confirm('Are you sure you want to delete this product?')) {
        dataManager.deleteProduct(id);
        app.loadProducts();
        app.updateNotificationCount();
    }
}

function editVendor(id) { console.log('Edit vendor:', id); }
function viewVendor(id) { console.log('View vendor:', id); }

function editCustomer(id) { console.log('Edit customer:', id); }
function viewCustomer(id) { console.log('View customer:', id); }

function viewPurchase(id) { console.log('View purchase:', id); }
function updatePurchaseStatus(id) { console.log('Update purchase status:', id); }

function viewSale(id) { console.log('View sale:', id); }
function updatePaymentStatus(id) { console.log('Update payment status:', id); }

function adjustStock(id) { console.log('Adjust stock:', id); }

// Initialize the application
const app = new InventoryApp();

// Complete Inventory Management System - All 14 Tables API Server
const express = require('express');
const cors = require('cors');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

console.log('🚀 Starting Complete Inventory Management Server...');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// PostgreSQL connection
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});

// Test database connection
pool.on('connect', () => {
    console.log('✅ Connected to PostgreSQL database');
});

pool.on('error', (err) => {
    console.error('❌ PostgreSQL connection error:', err);
});

// Utility function to execute queries
async function executeQuery(query, params = []) {
    const client = await pool.connect();
    try {
        const result = await client.query(query, params);
        return result;
    } catch (error) {
        console.error('Database query error:', error);
        throw error;
    } finally {
        client.release();
    }
}

// ==================== MASTER DATA TABLES APIs ====================

// 1. VENDOR APIs
app.get('/api/vendors', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM vendor ORDER BY company, vendor_code');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/vendors', async (req, res) => {
    try {
        const { company, vendor_code, vendor_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active, created_by } = req.body;
        const query = `
            INSERT INTO vendor (company, vendor_code, vendor_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, vendor_code, vendor_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.put('/api/vendors/:company/:vendor_code', async (req, res) => {
    try {
        const { company, vendor_code } = req.params;
        const { vendor_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active } = req.body;
        const query = `
            UPDATE vendor SET vendor_name = $3, address1 = $4, address2 = $5, location = $6, city = $7, state = $8, pin = $9, std_code = $10, phone = $11, mobile = $12, email = $13, website = $14, gst = $15, pan = $16, active = $17, updated = CURRENT_DATE
            WHERE company = $1 AND vendor_code = $2
            RETURNING *
        `;
        const result = await executeQuery(query, [company, vendor_code, vendor_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.delete('/api/vendors/:company/:vendor_code', async (req, res) => {
    try {
        const { company, vendor_code } = req.params;
        const result = await executeQuery('DELETE FROM vendor WHERE company = $1 AND vendor_code = $2 RETURNING *', [company, vendor_code]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 2. CUSTOMER APIs
app.get('/api/customers', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM customer ORDER BY company, customer_code');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/customers', async (req, res) => {
    try {
        const { company, customer_code, customer_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active, created_by } = req.body;
        const query = `
            INSERT INTO customer (company, customer_code, customer_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, customer_code, customer_name, address1, address2, location, city, state, pin, std_code, phone, mobile, email, website, gst, pan, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 3. PART APIs
app.get('/api/parts', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM part ORDER BY company, part_num');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/parts', async (req, res) => {
    try {
        const { company, part_num, part_desc, part_type, part_category, unit_id, pur_price, sell_price, hsn_code, tax_id, tax_percent, lot, batch, warranty, costing, active, created_by } = req.body;
        const query = `
            INSERT INTO part (company, part_num, part_desc, part_type, part_category, unit_id, pur_price, sell_price, hsn_code, tax_id, tax_percent, lot, batch, warranty, costing, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, part_num, part_desc, part_type, part_category, unit_id, pur_price, sell_price, hsn_code, tax_id, tax_percent, lot, batch, warranty, costing, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 4. UNIT APIs
app.get('/api/units', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM unit ORDER BY company, unit_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/units', async (req, res) => {
    try {
        const { company, unit_id, unit_desc, active, created_by } = req.body;
        const query = `
            INSERT INTO unit (company, unit_id, unit_desc, active, created_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, unit_id, unit_desc, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 5. USER APIs
app.get('/api/users', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM "user" ORDER BY company, user_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/users', async (req, res) => {
    try {
        const { company, user_id, user_key, active, created_by } = req.body;
        const query = `
            INSERT INTO "user" (company, user_id, user_key, active, created_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, user_id, user_key, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 6. USER ROLE APIs
app.get('/api/user-roles', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM user_role ORDER BY company, role_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/user-roles', async (req, res) => {
    try {
        const { company, role_id, role_desc, active, created_by } = req.body;
        const query = `
            INSERT INTO user_role (company, role_id, role_desc, active, created_by)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, role_id, role_desc, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 7. TAX APIs
app.get('/api/taxes', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM tax ORDER BY company, tax_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/taxes', async (req, res) => {
    try {
        const { company, tax_id, tax_desc, tax_percent, active, created_by } = req.body;
        const query = `
            INSERT INTO tax (company, tax_id, tax_desc, tax_percent, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, tax_id, tax_desc, tax_percent, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// ==================== TRANSACTION TABLES APIs ====================

// 8. INDENT/REQUEST APIs
app.get('/api/indents', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM indent_request ORDER BY company, indent_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/indents', async (req, res) => {
    try {
        const { company, indent_id, indent_date, indent_by, active, created_by } = req.body;
        const query = `
            INSERT INTO indent_request (company, indent_id, indent_date, indent_by, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, indent_id, indent_date, indent_by, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 9. PURCHASE ORDER APIs
app.get('/api/purchase-orders', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM purchase_order ORDER BY company, po_no');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/purchase-orders', async (req, res) => {
    try {
        const { company, po_no, po_date, vendor, indent_id, active, created_by } = req.body;
        const query = `
            INSERT INTO purchase_order (company, po_no, po_date, vendor, indent_id, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, po_no, po_date, vendor, indent_id, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 10. GOODS RECEIPT APIs
app.get('/api/goods-receipts', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM goods_receipt ORDER BY company, grn_no');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/goods-receipts', async (req, res) => {
    try {
        const { company, grn_no, grn_dt, vendor, po_no, active, created_by } = req.body;
        const query = `
            INSERT INTO goods_receipt (company, grn_no, grn_dt, vendor, po_no, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, grn_no, grn_dt, vendor, po_no, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 11. SALES INVOICE APIs
app.get('/api/sales-invoices', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM sales_invoice ORDER BY company, inv_no');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/sales-invoices', async (req, res) => {
    try {
        const { company, inv_no, inv_dt, customer, po_no, active, created_by } = req.body;
        const query = `
            INSERT INTO sales_invoice (company, inv_no, inv_dt, customer, po_no, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, inv_no, inv_dt, customer, po_no, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 12. ACCOUNTS PAYABLE APIs
app.get('/api/accounts-payable', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM accounts_payable ORDER BY company, ap_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/accounts-payable', async (req, res) => {
    try {
        const { company, ap_id, ap_date, vendor_code, vendor_invoice, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by } = req.body;
        const query = `
            INSERT INTO accounts_payable (company, ap_id, ap_date, vendor_code, vendor_invoice, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, ap_id, ap_date, vendor_code, vendor_invoice, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 13. ACCOUNTS RECEIVABLE APIs
app.get('/api/accounts-receivable', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM accounts_receivable ORDER BY company, ar_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/accounts-receivable', async (req, res) => {
    try {
        const { company, ar_id, ar_date, customer_code, invoice_number, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by } = req.body;
        const query = `
            INSERT INTO accounts_receivable (company, ar_id, ar_date, customer_code, invoice_number, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, ar_id, ar_date, customer_code, invoice_number, invoice_due_date, po_no, amount, paid_amount, due_amount, active, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// 14. STOCK UPDATE APIs
app.get('/api/stock-updates', async (req, res) => {
    try {
        const result = await executeQuery('SELECT * FROM stock_update ORDER BY company, update_id');
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/stock-updates', async (req, res) => {
    try {
        const { company, update_id, update_date, type, qty, created_by } = req.body;
        const query = `
            INSERT INTO stock_update (company, update_id, update_date, type, qty, created_by)
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *
        `;
        const result = await executeQuery(query, [company, update_id, update_date, type, qty, created_by]);
        res.json({ success: true, data: result.rows[0] });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Health check endpoint
app.get('/api/health', async (req, res) => {
    try {
        await executeQuery('SELECT 1');
        res.json({
            success: true,
            message: 'Complete Inventory Management System is running',
            timestamp: new Date().toISOString(),
            database: 'Connected'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Database connection failed',
            error: error.message
        });
    }
});

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index-complete.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`🌟 Complete Inventory Management Server running on http://localhost:${PORT}`);
    console.log(`📊 Database: PostgreSQL (inventory_management)`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log(`📋 All 14 Tables: Vendor, Customer, Part, Unit, User, UserRole, Tax, Indent, PO, GRN, Invoice, AP, AR, Stock`);
});

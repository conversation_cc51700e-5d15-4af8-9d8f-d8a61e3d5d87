// Setup script for Advanced Inventory Management System
const fs = require('fs').promises;
const path = require('path');

// Sample data for initial setup
const sampleData = {
    products: [
        {
            id: 'P1001',
            hsnCode: 'P1001',
            name: 'Gaming Laptop',
            category: 'Electronics',
            costPrice: 45000,
            sellingPrice: 52000,
            supplier: 'TechCorp',
            reorderLevel: 5,
            maxStockLevel: 50,
            currentStock: 12,
            createdAt: new Date().toISOString()
        },
        {
            id: 'P1002',
            hsnCode: 'P1002',
            name: 'Wireless Mouse',
            category: 'Accessories',
            costPrice: 1200,
            sellingPrice: 1440,
            supplier: 'AccessoryHub',
            reorderLevel: 20,
            maxStockLevel: 100,
            currentStock: 45,
            createdAt: new Date().toISOString()
        },
        {
            id: 'P1003',
            hsnCode: 'P1003',
            name: 'Mechanical Keyboard',
            category: 'Accessories',
            costPrice: 3500,
            sellingPrice: 4200,
            supplier: 'AccessoryHub',
            reorderLevel: 15,
            maxStockLevel: 80,
            currentStock: 23,
            createdAt: new Date().toISOString()
        },
        {
            id: 'P1004',
            hsnCode: 'P1004',
            name: '4K Monitor',
            category: 'Electronics',
            costPrice: 25000,
            sellingPrice: 30000,
            supplier: 'DisplayTech',
            reorderLevel: 8,
            maxStockLevel: 40,
            currentStock: 6,
            createdAt: new Date().toISOString()
        },
        {
            id: 'P1005',
            hsnCode: 'P1005',
            name: 'Graphics Card',
            category: 'Components',
            costPrice: 35000,
            sellingPrice: 42000,
            supplier: 'ComponentKing',
            reorderLevel: 6,
            maxStockLevel: 30,
            currentStock: 4,
            createdAt: new Date().toISOString()
        }
    ],
    vendors: [
        {
            id: 'V001',
            name: 'TechCorp',
            contactPerson: 'Rajesh Kumar',
            phone: '9876543210',
            email: '<EMAIL>',
            address: 'Mumbai, Maharashtra',
            paymentTerms: '30 Days',
            rating: 4.5,
            createdAt: new Date().toISOString()
        },
        {
            id: 'V002',
            name: 'AccessoryHub',
            contactPerson: 'Priya Sharma',
            phone: '9876543211',
            email: '<EMAIL>',
            address: 'Delhi, NCR',
            paymentTerms: '15 Days',
            rating: 4.2,
            createdAt: new Date().toISOString()
        },
        {
            id: 'V003',
            name: 'DisplayTech',
            contactPerson: 'Amit Singh',
            phone: '9876543212',
            email: '<EMAIL>',
            address: 'Bangalore, Karnataka',
            paymentTerms: '45 Days',
            rating: 4.7,
            createdAt: new Date().toISOString()
        },
        {
            id: 'V004',
            name: 'ComponentKing',
            contactPerson: 'Neha Gupta',
            phone: '9876543213',
            email: '<EMAIL>',
            address: 'Pune, Maharashtra',
            paymentTerms: '30 Days',
            rating: 4.3,
            createdAt: new Date().toISOString()
        }
    ],
    customers: [
        {
            id: 'C001',
            name: 'Tech Solutions Ltd',
            contactPerson: 'Suresh Reddy',
            phone: '9123456789',
            email: '<EMAIL>',
            address: 'Hyderabad, Telangana',
            type: 'B2B',
            creditLimit: 500000,
            createdAt: new Date().toISOString()
        },
        {
            id: 'C002',
            name: 'Gaming Zone',
            contactPerson: 'Anita Joshi',
            phone: '9123456790',
            email: '<EMAIL>',
            address: 'Mumbai, Maharashtra',
            type: 'B2C',
            creditLimit: 100000,
            createdAt: new Date().toISOString()
        },
        {
            id: 'C003',
            name: 'Office Supplies Co',
            contactPerson: 'Rohit Mehta',
            phone: '9123456791',
            email: '<EMAIL>',
            address: 'Delhi, NCR',
            type: 'B2B',
            creditLimit: 300000,
            createdAt: new Date().toISOString()
        },
        {
            id: 'C004',
            name: 'Digital World',
            contactPerson: 'Kavya Nair',
            phone: '9123456792',
            email: '<EMAIL>',
            address: 'Kochi, Kerala',
            type: 'B2C',
            creditLimit: 150000,
            createdAt: new Date().toISOString()
        }
    ],
    purchases: [
        {
            id: 'PUR1001',
            date: '2024-01-15',
            productId: 'P1001',
            productName: 'Gaming Laptop',
            vendor: 'TechCorp',
            quantity: 20,
            unitCost: 45000,
            totalAmount: 900000,
            status: 'Received',
            createdAt: new Date().toISOString()
        },
        {
            id: 'PUR1002',
            date: '2024-01-16',
            productId: 'P1002',
            productName: 'Wireless Mouse',
            vendor: 'AccessoryHub',
            quantity: 50,
            unitCost: 1200,
            totalAmount: 60000,
            status: 'Received',
            createdAt: new Date().toISOString()
        },
        {
            id: 'PUR1003',
            date: '2024-01-17',
            productId: 'P1005',
            productName: 'Graphics Card',
            vendor: 'ComponentKing',
            quantity: 10,
            unitCost: 35000,
            totalAmount: 350000,
            status: 'Pending',
            createdAt: new Date().toISOString()
        }
    ],
    sales: [
        {
            id: 'SAL2001',
            date: '2024-01-20',
            customerId: 'C001',
            customerName: 'Tech Solutions Ltd',
            productId: 'P1001',
            productName: 'Gaming Laptop',
            quantity: 5,
            unitPrice: 52000,
            totalAmount: 260000,
            paymentStatus: 'Paid',
            paymentMethod: 'Bank Transfer',
            createdAt: new Date().toISOString()
        },
        {
            id: 'SAL2002',
            date: '2024-01-21',
            customerId: 'C002',
            customerName: 'Gaming Zone',
            productId: 'P1002',
            productName: 'Wireless Mouse',
            quantity: 10,
            unitPrice: 1440,
            totalAmount: 14400,
            paymentStatus: 'Paid',
            paymentMethod: 'Cash',
            createdAt: new Date().toISOString()
        },
        {
            id: 'SAL2003',
            date: '2024-01-22',
            customerId: 'C003',
            customerName: 'Office Supplies Co',
            productId: 'P1003',
            productName: 'Mechanical Keyboard',
            quantity: 15,
            unitPrice: 4200,
            totalAmount: 63000,
            paymentStatus: 'Pending',
            paymentMethod: 'Credit',
            createdAt: new Date().toISOString()
        }
    ],
    lastUpdated: new Date().toISOString()
};

async function setupSystem() {
    console.log('🚀 Setting up Advanced Inventory Management System...\n');

    try {
        // Create data directory
        console.log('📁 Creating data directory...');
        await fs.mkdir(path.join(__dirname, 'data'), { recursive: true });
        console.log('✅ Data directory created\n');

        // Create sample data file
        console.log('📊 Creating sample data...');
        const dataFile = path.join(__dirname, 'data', 'inventory.json');
        await fs.writeFile(dataFile, JSON.stringify(sampleData, null, 2));
        console.log('✅ Sample data created\n');

        // Create screenshots directory
        console.log('📸 Creating screenshots directory...');
        await fs.mkdir(path.join(__dirname, 'screenshots'), { recursive: true });
        console.log('✅ Screenshots directory created\n');

        // Create logs directory
        console.log('📝 Creating logs directory...');
        await fs.mkdir(path.join(__dirname, 'logs'), { recursive: true });
        console.log('✅ Logs directory created\n');

        // Display setup summary
        console.log('🎉 SETUP COMPLETE!\n');
        console.log('📋 System Summary:');
        console.log(`   • Products: ${sampleData.products.length} items`);
        console.log(`   • Vendors: ${sampleData.vendors.length} suppliers`);
        console.log(`   • Customers: ${sampleData.customers.length} clients`);
        console.log(`   • Purchases: ${sampleData.purchases.length} transactions`);
        console.log(`   • Sales: ${sampleData.sales.length} transactions`);
        console.log('\n🚀 Ready to start the server!');
        console.log('   Run: npm start');
        console.log('   Open: http://localhost:3000\n');

        // Display critical stock alerts
        const criticalItems = sampleData.products.filter(p => p.currentStock <= p.reorderLevel);
        if (criticalItems.length > 0) {
            console.log('🚨 CRITICAL STOCK ALERTS:');
            criticalItems.forEach(item => {
                console.log(`   • ${item.name}: ${item.currentStock} units (Reorder: ${item.reorderLevel})`);
            });
            console.log('');
        }

        console.log('📖 For detailed documentation, see README.md');
        console.log('🔧 For API documentation, visit http://localhost:3000/api after starting the server');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    }
}

// Run setup if called directly
if (require.main === module) {
    setupSystem();
}

module.exports = { setupSystem, sampleData };

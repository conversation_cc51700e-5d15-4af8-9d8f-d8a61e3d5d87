// Simple Database Setup Script
const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function setupDatabase() {
    console.log('🚀 Setting up PostgreSQL database...\n');

    try {
        // Test connection
        await pool.query('SELECT 1');
        console.log('✅ Connected to PostgreSQL\n');

        // Drop existing tables if they exist (for clean setup)
        console.log('🧹 Cleaning up existing tables...');
        const dropQueries = [
            'DROP VIEW IF EXISTS inventory_status CASCADE',
            'DROP VIEW IF EXISTS sales_summary CASCADE',
            'DROP VIEW IF EXISTS purchase_summary CASCADE',
            'DROP TABLE IF EXISTS stock_movements CASCADE',
            'DROP TABLE IF EXISTS sales CASCADE',
            'DROP TABLE IF EXISTS purchases CASCADE',
            'DROP TABLE IF EXISTS products CASCADE',
            'DROP TABLE IF EXISTS customers CASCADE',
            'DROP TABLE IF EXISTS vendors CASCADE',
            'DROP TABLE IF EXISTS categories CASCADE',
            'DROP TYPE IF EXISTS customer_type CASCADE',
            'DROP TYPE IF EXISTS transaction_status CASCADE',
            'DROP TYPE IF EXISTS payment_status CASCADE',
            'DROP TYPE IF EXISTS payment_method CASCADE',
            'DROP TYPE IF EXISTS stock_status CASCADE',
            'DROP SEQUENCE IF EXISTS vendor_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS customer_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS product_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS purchase_code_seq CASCADE',
            'DROP SEQUENCE IF EXISTS sale_code_seq CASCADE'
        ];

        for (const query of dropQueries) {
            try {
                await pool.query(query);
            } catch (error) {
                // Ignore errors for non-existent objects
            }
        }
        console.log('✅ Cleanup complete\n');

        // Create extensions and types
        console.log('📦 Creating extensions and types...');
        await pool.query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
        
        await pool.query("CREATE TYPE customer_type AS ENUM ('B2B', 'B2C')");
        await pool.query("CREATE TYPE transaction_status AS ENUM ('Pending', 'Received', 'Partial', 'Cancelled')");
        await pool.query("CREATE TYPE payment_status AS ENUM ('Paid', 'Pending', 'Partial', 'Overdue')");
        await pool.query("CREATE TYPE payment_method AS ENUM ('Cash', 'Card', 'UPI', 'Bank Transfer', 'Credit', 'Cheque')");
        await pool.query("CREATE TYPE stock_status AS ENUM ('Critical', 'Low Stock', 'Adequate', 'Overstocked')");
        console.log('✅ Extensions and types created\n');

        // Create sequences
        console.log('🔢 Creating sequences...');
        await pool.query('CREATE SEQUENCE vendor_code_seq START 1001');
        await pool.query('CREATE SEQUENCE customer_code_seq START 2001');
        await pool.query('CREATE SEQUENCE product_code_seq START 3001');
        await pool.query('CREATE SEQUENCE purchase_code_seq START 4001');
        await pool.query('CREATE SEQUENCE sale_code_seq START 5001');
        console.log('✅ Sequences created\n');

        // Create tables
        console.log('📋 Creating tables...');
        
        // Categories table
        await pool.query(`
            CREATE TABLE categories (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                name VARCHAR(100) UNIQUE NOT NULL,
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Vendors table
        await pool.query(`
            CREATE TABLE vendors (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                vendor_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(255) NOT NULL,
                contact_person VARCHAR(255) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                address TEXT NOT NULL,
                city VARCHAR(100),
                state VARCHAR(100),
                country VARCHAR(100) DEFAULT 'India',
                postal_code VARCHAR(10),
                payment_terms VARCHAR(50) DEFAULT '30 Days',
                rating DECIMAL(2,1) CHECK (rating >= 1.0 AND rating <= 5.0) DEFAULT 4.0,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Customers table
        await pool.query(`
            CREATE TABLE customers (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                customer_code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(255) NOT NULL,
                contact_person VARCHAR(255) NOT NULL,
                phone VARCHAR(20) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                address TEXT NOT NULL,
                city VARCHAR(100),
                state VARCHAR(100),
                country VARCHAR(100) DEFAULT 'India',
                postal_code VARCHAR(10),
                customer_type customer_type NOT NULL DEFAULT 'B2C',
                credit_limit DECIMAL(15,2) DEFAULT 100000.00,
                current_balance DECIMAL(15,2) DEFAULT 0.00,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Products table
        await pool.query(`
            CREATE TABLE products (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                product_code VARCHAR(20) UNIQUE NOT NULL,
                hsn_code VARCHAR(20) NOT NULL,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                category_id UUID REFERENCES categories(id),
                cost_price DECIMAL(15,2) NOT NULL CHECK (cost_price >= 0),
                selling_price DECIMAL(15,2) NOT NULL CHECK (selling_price >= 0),
                vendor_id UUID REFERENCES vendors(id),
                reorder_level INTEGER NOT NULL DEFAULT 10 CHECK (reorder_level >= 0),
                max_stock_level INTEGER NOT NULL DEFAULT 100 CHECK (max_stock_level >= reorder_level),
                current_stock INTEGER NOT NULL DEFAULT 0 CHECK (current_stock >= 0),
                unit VARCHAR(20) DEFAULT 'pcs',
                weight DECIMAL(10,3),
                dimensions VARCHAR(100),
                barcode VARCHAR(50),
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Purchases table
        await pool.query(`
            CREATE TABLE purchases (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                purchase_code VARCHAR(20) UNIQUE NOT NULL,
                purchase_date DATE NOT NULL DEFAULT CURRENT_DATE,
                vendor_id UUID NOT NULL REFERENCES vendors(id),
                product_id UUID NOT NULL REFERENCES products(id),
                quantity INTEGER NOT NULL CHECK (quantity > 0),
                unit_cost DECIMAL(15,2) NOT NULL CHECK (unit_cost >= 0),
                total_amount DECIMAL(15,2) NOT NULL CHECK (total_amount >= 0),
                tax_amount DECIMAL(15,2) DEFAULT 0.00,
                discount_amount DECIMAL(15,2) DEFAULT 0.00,
                final_amount DECIMAL(15,2) NOT NULL,
                status transaction_status DEFAULT 'Pending',
                expected_delivery_date DATE,
                actual_delivery_date DATE,
                invoice_number VARCHAR(50),
                notes TEXT,
                created_by VARCHAR(100),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Sales table
        await pool.query(`
            CREATE TABLE sales (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                sale_code VARCHAR(20) UNIQUE NOT NULL,
                sale_date DATE NOT NULL DEFAULT CURRENT_DATE,
                customer_id UUID NOT NULL REFERENCES customers(id),
                product_id UUID NOT NULL REFERENCES products(id),
                quantity INTEGER NOT NULL CHECK (quantity > 0),
                unit_price DECIMAL(15,2) NOT NULL CHECK (unit_price >= 0),
                total_amount DECIMAL(15,2) NOT NULL CHECK (total_amount >= 0),
                tax_amount DECIMAL(15,2) DEFAULT 0.00,
                discount_amount DECIMAL(15,2) DEFAULT 0.00,
                final_amount DECIMAL(15,2) NOT NULL,
                payment_status payment_status DEFAULT 'Pending',
                payment_method payment_method DEFAULT 'Cash',
                payment_date DATE,
                invoice_number VARCHAR(50),
                notes TEXT,
                created_by VARCHAR(100),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Stock movements table
        await pool.query(`
            CREATE TABLE stock_movements (
                id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                product_id UUID NOT NULL REFERENCES products(id),
                movement_type VARCHAR(20) NOT NULL,
                quantity INTEGER NOT NULL,
                reference_type VARCHAR(20),
                reference_id UUID,
                previous_stock INTEGER NOT NULL,
                new_stock INTEGER NOT NULL,
                notes TEXT,
                created_by VARCHAR(100),
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        `);

        console.log('✅ Tables created\n');

        // Create indexes
        console.log('📊 Creating indexes...');
        const indexes = [
            'CREATE INDEX idx_products_category ON products(category_id)',
            'CREATE INDEX idx_products_vendor ON products(vendor_id)',
            'CREATE INDEX idx_products_stock_status ON products(current_stock, reorder_level)',
            'CREATE INDEX idx_purchases_vendor ON purchases(vendor_id)',
            'CREATE INDEX idx_purchases_product ON purchases(product_id)',
            'CREATE INDEX idx_purchases_date ON purchases(purchase_date)',
            'CREATE INDEX idx_sales_customer ON sales(customer_id)',
            'CREATE INDEX idx_sales_product ON sales(product_id)',
            'CREATE INDEX idx_sales_date ON sales(sale_date)',
            'CREATE INDEX idx_stock_movements_product ON stock_movements(product_id)',
            'CREATE INDEX idx_stock_movements_date ON stock_movements(created_at)'
        ];

        for (const index of indexes) {
            await pool.query(index);
        }
        console.log('✅ Indexes created\n');

        // Create inventory view
        console.log('👁️ Creating views...');
        await pool.query(`
            CREATE VIEW inventory_status AS
            SELECT 
                p.id,
                p.product_code,
                p.hsn_code,
                p.name,
                c.name as category_name,
                p.current_stock,
                p.reorder_level,
                p.max_stock_level,
                p.cost_price,
                p.selling_price,
                (p.current_stock * p.cost_price) as stock_value,
                v.name as vendor_name,
                v.phone as vendor_phone,
                v.email as vendor_email,
                CASE 
                    WHEN p.current_stock <= p.reorder_level THEN 'Critical'::stock_status
                    WHEN p.current_stock <= (p.reorder_level * 1.5) THEN 'Low Stock'::stock_status
                    WHEN p.current_stock >= p.max_stock_level THEN 'Overstocked'::stock_status
                    ELSE 'Adequate'::stock_status
                END as stock_status,
                p.updated_at as last_updated
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN vendors v ON p.vendor_id = v.id
            WHERE p.is_active = TRUE
        `);
        console.log('✅ Views created\n');

        console.log('🎉 Database schema setup complete!\n');
        return true;

    } catch (error) {
        console.error('❌ Database setup failed:', error.message);
        return false;
    }
}

// Run setup
setupDatabase()
    .then(success => {
        if (success) {
            console.log('✅ Database is ready for sample data insertion!');
            console.log('   Run: node insert-sample-data.js');
        }
        pool.end();
    })
    .catch(error => {
        console.error('❌ Setup failed:', error);
        pool.end();
        process.exit(1);
    });

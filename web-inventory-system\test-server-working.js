// Test if server is working
const http = require('http');

function testServer() {
    const options = {
        hostname: 'localhost',
        port: 3000,
        path: '/api/health',
        method: 'GET'
    };

    const req = http.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        res.on('end', () => {
            console.log('✅ Server is working!');
            console.log('Response:', data);
        });
    });

    req.on('error', (error) => {
        console.log('❌ Server not responding:', error.message);
        console.log('💡 Try starting the server with: node server-simple.js');
    });

    req.setTimeout(3000, () => {
        req.destroy();
        console.log('❌ Server timeout - not responding');
    });

    req.end();
}

testServer();

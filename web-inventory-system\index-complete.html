<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Inventory Management System - All 14 Tables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-bg: #ecf0f1;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .sidebar {
            background: white;
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            padding: 0;
        }

        .sidebar .nav-link {
            color: var(--primary-color);
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }

        .main-content {
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin-bottom: 30px;
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.1);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .status-active {
            background-color: var(--success-color);
            color: white;
        }

        .status-inactive {
            background-color: var(--danger-color);
            color: white;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 50px;
        }

        .loading i {
            font-size: 3rem;
            color: var(--secondary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .section-hidden {
            display: none;
        }

        .section-active {
            display: block;
        }

        .master-data-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .transaction-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .financial-section {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .operational-section {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-warehouse me-2"></i>
                Complete Inventory Management System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-database me-1"></i>
                    All 14 Tables | Multi-Company Support
                </span>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar">
                <nav class="nav flex-column">
                    <!-- Master Data Section -->
                    <div class="nav-section">
                        <h6 class="nav-header text-muted p-3 mb-0">MASTER DATA</h6>
                        <a class="nav-link active" href="#" onclick="showSection('vendors')">
                            <i class="fas fa-truck"></i>Vendors
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('customers')">
                            <i class="fas fa-users"></i>Customers
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('parts')">
                            <i class="fas fa-cogs"></i>Parts
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('units')">
                            <i class="fas fa-ruler"></i>Units
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('users')">
                            <i class="fas fa-user"></i>Users
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('user-roles')">
                            <i class="fas fa-user-shield"></i>User Roles
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('taxes')">
                            <i class="fas fa-percentage"></i>Taxes
                        </a>
                    </div>

                    <!-- Transaction Data Section -->
                    <div class="nav-section">
                        <h6 class="nav-header text-muted p-3 mb-0 mt-3">TRANSACTIONS</h6>
                        <a class="nav-link" href="#" onclick="showSection('indents')">
                            <i class="fas fa-clipboard-list"></i>Indent/Request
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('purchase-orders')">
                            <i class="fas fa-shopping-cart"></i>Purchase Orders
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('goods-receipts')">
                            <i class="fas fa-box-open"></i>Goods Receipt
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('sales-invoices')">
                            <i class="fas fa-file-invoice"></i>Sales Invoice
                        </a>
                    </div>

                    <!-- Financial Data Section -->
                    <div class="nav-section">
                        <h6 class="nav-header text-muted p-3 mb-0 mt-3">FINANCIAL</h6>
                        <a class="nav-link" href="#" onclick="showSection('accounts-payable')">
                            <i class="fas fa-money-bill-wave"></i>Accounts Payable
                        </a>
                        <a class="nav-link" href="#" onclick="showSection('accounts-receivable')">
                            <i class="fas fa-hand-holding-usd"></i>Accounts Receivable
                        </a>
                    </div>

                    <!-- Operational Data Section -->
                    <div class="nav-section">
                        <h6 class="nav-header text-muted p-3 mb-0 mt-3">OPERATIONAL</h6>
                        <a class="nav-link" href="#" onclick="showSection('stock-updates')">
                            <i class="fas fa-warehouse"></i>Stock Updates
                        </a>
                    </div>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <!-- Dashboard Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card master-data-section">
                            <div class="stats-number" id="vendorCount">0</div>
                            <div class="stats-label">Vendors</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card master-data-section">
                            <div class="stats-number" id="customerCount">0</div>
                            <div class="stats-label">Customers</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card master-data-section">
                            <div class="stats-number" id="partCount">0</div>
                            <div class="stats-label">Parts</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card transaction-section">
                            <div class="stats-number" id="poCount">0</div>
                            <div class="stats-label">Purchase Orders</div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stats-card transaction-section">
                            <div class="stats-number" id="indentCount">0</div>
                            <div class="stats-label">Indent Requests</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card transaction-section">
                            <div class="stats-number" id="invoiceCount">0</div>
                            <div class="stats-label">Sales Invoices</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card financial-section">
                            <div class="stats-number" id="apCount">0</div>
                            <div class="stats-label">Accounts Payable</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card operational-section">
                            <div class="stats-number" id="stockCount">0</div>
                            <div class="stats-label">Stock Updates</div>
                        </div>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div class="loading" id="loading">
                    <i class="fas fa-spinner"></i>
                    <p class="mt-3">Loading data...</p>
                </div>

                <!-- Dynamic Content Sections -->
                <div id="content-sections">
                    <!-- Content will be dynamically generated based on selected section -->
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Modal -->
    <div class="modal fade" id="dataModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">Add New Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="dataForm">
                        <div id="formFields">
                            <!-- Dynamic form fields will be generated here -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveData()">Save</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="complete-inventory.js"></script>
</body>
</html>

// Simple Database Setup Script - Tables Only
const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function setupTablesOnly() {
    console.log('🚀 Setting up Complete Inventory Management Database Tables...\n');

    try {
        // Test connection
        await pool.query('SELECT 1');
        console.log('✅ Connected to PostgreSQL\n');

        // Drop existing tables if they exist
        console.log('🗑️ Dropping existing tables...');
        const dropTables = [
            'DROP TABLE IF EXISTS stock_update CASCADE',
            'DROP TABLE IF EXISTS accounts_receivable CASCADE',
            'DROP TABLE IF EXISTS accounts_payable CASCADE',
            'DROP TABLE IF EXISTS sales_invoice_data CASCADE',
            'DROP TABLE IF EXISTS sales_invoice CASCADE',
            'DROP TABLE IF EXISTS goods_receipt_data CASCADE',
            'DROP TABLE IF EXISTS goods_receipt CASCADE',
            'DROP TABLE IF EXISTS purchase_order_data CASCADE',
            'DROP TABLE IF EXISTS purchase_order CASCADE',
            'DROP TABLE IF EXISTS indent_request_data CASCADE',
            'DROP TABLE IF EXISTS indent_request CASCADE',
            'DROP TABLE IF EXISTS tax CASCADE',
            'DROP TABLE IF EXISTS user_role CASCADE',
            'DROP TABLE IF EXISTS "user" CASCADE',
            'DROP TABLE IF EXISTS unit CASCADE',
            'DROP TABLE IF EXISTS part CASCADE',
            'DROP TABLE IF EXISTS customer CASCADE',
            'DROP TABLE IF EXISTS vendor CASCADE'
        ];

        for (const dropStmt of dropTables) {
            try {
                await pool.query(dropStmt);
            } catch (error) {
                // Ignore errors for non-existent tables
            }
        }

        // Create all 14 tables
        console.log('📋 Creating all 14 tables...\n');

        // 1. VENDOR TABLE
        await pool.query(`
            CREATE TABLE vendor (
                company NUMERIC NOT NULL,
                vendor_code NUMERIC UNIQUE NOT NULL,
                vendor_name VARCHAR(100) NOT NULL,
                address1 VARCHAR(200),
                address2 VARCHAR(200),
                location VARCHAR(100),
                city VARCHAR(100),
                state VARCHAR(100),
                pin NUMERIC,
                std_code NUMERIC,
                phone NUMERIC,
                mobile NUMERIC,
                email VARCHAR(100),
                website VARCHAR(200),
                gst VARCHAR(16),
                pan VARCHAR(10),
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, vendor_code)
            )
        `);
        console.log('✅ VENDOR table created');

        // 2. CUSTOMER TABLE
        await pool.query(`
            CREATE TABLE customer (
                company NUMERIC NOT NULL,
                customer_code NUMERIC UNIQUE NOT NULL,
                customer_name VARCHAR(100) NOT NULL,
                address1 VARCHAR(200),
                address2 VARCHAR(200),
                location VARCHAR(100),
                city VARCHAR(100),
                state VARCHAR(100),
                pin NUMERIC,
                std_code NUMERIC,
                phone NUMERIC,
                mobile NUMERIC,
                email VARCHAR(100),
                website VARCHAR(200),
                gst VARCHAR(16),
                pan VARCHAR(10),
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, customer_code)
            )
        `);
        console.log('✅ CUSTOMER table created');

        // 3. UNIT TABLE
        await pool.query(`
            CREATE TABLE unit (
                company NUMERIC NOT NULL,
                unit_id NUMERIC UNIQUE NOT NULL,
                unit_desc VARCHAR(100) NOT NULL,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, unit_id)
            )
        `);
        console.log('✅ UNIT table created');

        // 4. TAX TABLE
        await pool.query(`
            CREATE TABLE tax (
                company NUMERIC NOT NULL,
                tax_id NUMERIC UNIQUE NOT NULL,
                tax_desc VARCHAR(100) NOT NULL,
                tax_percent NUMERIC,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, tax_id)
            )
        `);
        console.log('✅ TAX table created');

        // 5. PART TABLE
        await pool.query(`
            CREATE TABLE part (
                company NUMERIC NOT NULL,
                part_num NUMERIC UNIQUE NOT NULL,
                part_desc VARCHAR(200) NOT NULL,
                part_type VARCHAR(100),
                part_category VARCHAR(100),
                unit_id NUMERIC,
                pur_price NUMERIC,
                sell_price NUMERIC,
                hsn_code VARCHAR(20),
                tax_id NUMERIC,
                tax_percent NUMERIC,
                lot BOOLEAN DEFAULT FALSE,
                batch BOOLEAN DEFAULT FALSE,
                warranty BOOLEAN DEFAULT FALSE,
                costing VARCHAR(50),
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, part_num)
            )
        `);
        console.log('✅ PART table created');

        // 6. USER TABLE
        await pool.query(`
            CREATE TABLE "user" (
                company NUMERIC NOT NULL,
                user_id VARCHAR(50) UNIQUE NOT NULL,
                user_key VARCHAR(50),
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, user_id)
            )
        `);
        console.log('✅ USER table created');

        // 7. USER ROLE TABLE
        await pool.query(`
            CREATE TABLE user_role (
                company NUMERIC NOT NULL,
                role_id NUMERIC UNIQUE NOT NULL,
                role_desc VARCHAR(100) NOT NULL,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, role_id)
            )
        `);
        console.log('✅ USER_ROLE table created');

        // 8. INDENT REQUEST TABLE
        await pool.query(`
            CREATE TABLE indent_request (
                company NUMERIC NOT NULL,
                indent_id NUMERIC UNIQUE NOT NULL,
                indent_date DATE DEFAULT CURRENT_DATE,
                indent_by VARCHAR(50),
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, indent_id)
            )
        `);
        console.log('✅ INDENT_REQUEST table created');

        // 9. PURCHASE ORDER TABLE
        await pool.query(`
            CREATE TABLE purchase_order (
                company NUMERIC NOT NULL,
                po_no NUMERIC UNIQUE NOT NULL,
                po_date DATE DEFAULT CURRENT_DATE,
                vendor NUMERIC,
                indent_id NUMERIC,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, po_no)
            )
        `);
        console.log('✅ PURCHASE_ORDER table created');

        // 10. GOODS RECEIPT TABLE
        await pool.query(`
            CREATE TABLE goods_receipt (
                company NUMERIC NOT NULL,
                grn_no NUMERIC UNIQUE NOT NULL,
                grn_dt DATE DEFAULT CURRENT_DATE,
                vendor NUMERIC,
                po_no NUMERIC,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, grn_no)
            )
        `);
        console.log('✅ GOODS_RECEIPT table created');

        // 11. SALES INVOICE TABLE
        await pool.query(`
            CREATE TABLE sales_invoice (
                company NUMERIC NOT NULL,
                inv_no NUMERIC UNIQUE NOT NULL,
                inv_dt DATE DEFAULT CURRENT_DATE,
                customer NUMERIC,
                po_no NUMERIC,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, inv_no)
            )
        `);
        console.log('✅ SALES_INVOICE table created');

        // 12. ACCOUNTS PAYABLE TABLE
        await pool.query(`
            CREATE TABLE accounts_payable (
                company NUMERIC NOT NULL,
                ap_id NUMERIC UNIQUE NOT NULL,
                ap_date DATE DEFAULT CURRENT_DATE,
                vendor_code NUMERIC,
                vendor_invoice VARCHAR(50),
                invoice_due_date DATE,
                po_no NUMERIC,
                amount NUMERIC,
                paid_amount NUMERIC,
                due_amount NUMERIC,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, ap_id)
            )
        `);
        console.log('✅ ACCOUNTS_PAYABLE table created');

        // 13. ACCOUNTS RECEIVABLE TABLE
        await pool.query(`
            CREATE TABLE accounts_receivable (
                company NUMERIC NOT NULL,
                ar_id NUMERIC UNIQUE NOT NULL,
                ar_date DATE DEFAULT CURRENT_DATE,
                customer_code NUMERIC,
                invoice_number VARCHAR(50),
                invoice_due_date DATE,
                po_no VARCHAR(50),
                amount NUMERIC,
                paid_amount NUMERIC,
                due_amount NUMERIC,
                active BOOLEAN DEFAULT TRUE,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, ar_id)
            )
        `);
        console.log('✅ ACCOUNTS_RECEIVABLE table created');

        // 14. STOCK UPDATE TABLE
        await pool.query(`
            CREATE TABLE stock_update (
                company NUMERIC NOT NULL,
                update_id NUMERIC UNIQUE NOT NULL,
                update_date DATE DEFAULT CURRENT_DATE,
                type VARCHAR(10),
                qty NUMERIC,
                created_by VARCHAR(50),
                updated DATE DEFAULT CURRENT_DATE,
                PRIMARY KEY (company, update_id)
            )
        `);
        console.log('✅ STOCK_UPDATE table created');

        // Create indexes
        console.log('\n📊 Creating indexes...');
        const indexes = [
            'CREATE INDEX idx_vendor_company ON vendor(company)',
            'CREATE INDEX idx_customer_company ON customer(company)',
            'CREATE INDEX idx_part_company ON part(company)',
            'CREATE INDEX idx_unit_company ON unit(company)',
            'CREATE INDEX idx_user_company ON "user"(company)',
            'CREATE INDEX idx_user_role_company ON user_role(company)',
            'CREATE INDEX idx_tax_company ON tax(company)',
            'CREATE INDEX idx_indent_company ON indent_request(company)',
            'CREATE INDEX idx_po_company ON purchase_order(company)',
            'CREATE INDEX idx_grn_company ON goods_receipt(company)',
            'CREATE INDEX idx_invoice_company ON sales_invoice(company)',
            'CREATE INDEX idx_ap_company ON accounts_payable(company)',
            'CREATE INDEX idx_ar_company ON accounts_receivable(company)',
            'CREATE INDEX idx_stock_update_company ON stock_update(company)'
        ];

        for (const indexStmt of indexes) {
            try {
                await pool.query(indexStmt);
            } catch (error) {
                // Ignore errors for existing indexes
            }
        }

        console.log('✅ Indexes created');

        // Verify table creation
        console.log('\n🔍 Verifying all 14 tables...\n');
        
        const tables = [
            'vendor', 'customer', 'part', 'unit', '"user"', 'user_role', 'tax',
            'indent_request', 'purchase_order', 'goods_receipt', 'sales_invoice',
            'accounts_payable', 'accounts_receivable', 'stock_update'
        ];

        for (const table of tables) {
            try {
                const result = await pool.query(`SELECT COUNT(*) FROM ${table}`);
                const count = result.rows[0].count;
                console.log(`✅ ${table.replace('"', '').replace('"', '').toUpperCase()}: Ready (${count} records)`);
            } catch (error) {
                console.log(`❌ ${table.toUpperCase()}: Error - ${error.message}`);
            }
        }

        console.log('\n🎉 Complete Inventory Management Database Setup Completed!');
        console.log('\n📋 Summary:');
        console.log('   • All 14 tables created successfully');
        console.log('   • Indexes created for performance');
        console.log('   • Multi-company support enabled');
        console.log('   • Ready for data entry');
        console.log('\n🚀 You can now start the server with: node server-complete.js');

    } catch (error) {
        console.error('❌ Setup failed:', error);
        process.exit(1);
    } finally {
        await pool.end();
    }
}

// Run setup if called directly
if (require.main === module) {
    setupTablesOnly();
}

module.exports = { setupTablesOnly };

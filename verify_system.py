import pandas as pd
import os

def verify_inventory_system():
    """
    Verify the created inventory management system
    """
    filename = "Advanced_Inventory_Management_System.xlsx"
    
    if not os.path.exists(filename):
        print("❌ File not found!")
        return
    
    print("🔍 VERIFYING INVENTORY MANAGEMENT SYSTEM")
    print("=" * 60)
    
    try:
        # Read all sheets
        excel_file = pd.ExcelFile(filename)
        sheets = excel_file.sheet_names
        
        print(f"📁 File: {filename}")
        print(f"📊 Total Sheets: {len(sheets)}")
        print(f"📋 Sheet Names: {', '.join(sheets)}")
        print("\n" + "=" * 60)
        
        # Verify each sheet
        for i, sheet_name in enumerate(sheets, 1):
            print(f"\n{i}. 📄 SHEET: {sheet_name}")
            print("-" * 40)
            
            df = pd.read_excel(filename, sheet_name=sheet_name)
            print(f"   📏 Dimensions: {df.shape[0]} rows × {df.shape[1]} columns")
            print(f"   📋 Columns: {', '.join(df.columns[:5])}{'...' if len(df.columns) > 5 else ''}")
            
            # Show sample data for key sheets
            if sheet_name in ['Products', 'Customers', 'Vendors']:
                print(f"   📊 Sample Records:")
                for idx, row in df.head(3).iterrows():
                    if sheet_name == 'Products':
                        print(f"      • {row.iloc[1]} - ₹{row.iloc[4]:,}")
                    elif sheet_name == 'Customers':
                        print(f"      • {row.iloc[1]} ({row.iloc[6]})")
                    elif sheet_name == 'Vendors':
                        print(f"      • {row.iloc[1]} - {row.iloc[5]}")
            
            elif sheet_name == 'Inventory':
                critical_items = df[df['Status'].str.contains('CRITICAL', na=False)]
                if len(critical_items) > 0:
                    print(f"   🚨 CRITICAL STOCK ALERTS: {len(critical_items)} items")
                    for _, item in critical_items.head(3).iterrows():
                        print(f"      • {item['Product Name']}: {item['Current Stock']} units")
                else:
                    print(f"   ✅ No critical stock alerts")
            
            elif sheet_name == 'Dashboard':
                print(f"   📈 Key Metrics Available:")
                for idx, row in df.head(8).iterrows():
                    if pd.notna(row.iloc[0]) and row.iloc[0] != '':
                        print(f"      • {row.iloc[0]}: {row.iloc[1]}")
        
        print("\n" + "=" * 60)
        print("✅ SYSTEM VERIFICATION COMPLETE")
        print("\n🎯 SYSTEM CAPABILITIES:")
        print("   • Complete product catalog management")
        print("   • Customer and vendor databases")
        print("   • Purchase and sales tracking")
        print("   • Real-time inventory monitoring")
        print("   • Automated stock alerts")
        print("   • Business analytics and reporting")
        print("   • Data entry templates")
        
        print("\n🚀 READY FOR USE!")
        print("   Open the Excel file to start managing your inventory")
        
    except Exception as e:
        print(f"❌ Error verifying system: {str(e)}")

if __name__ == "__main__":
    verify_inventory_system()

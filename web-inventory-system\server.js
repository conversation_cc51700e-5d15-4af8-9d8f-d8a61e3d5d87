// Simple Node.js Express Server for Inventory Management System
const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// Data file path
const DATA_FILE = path.join(__dirname, 'data', 'inventory.json');

// Ensure data directory exists
async function ensureDataDirectory() {
    try {
        await fs.mkdir(path.join(__dirname, 'data'), { recursive: true });
    } catch (error) {
        console.error('Error creating data directory:', error);
    }
}

// Load data from file
async function loadData() {
    try {
        const data = await fs.readFile(DATA_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        // Return default data if file doesn't exist
        return {
            products: [],
            vendors: [],
            customers: [],
            purchases: [],
            sales: [],
            lastUpdated: new Date().toISOString()
        };
    }
}

// Save data to file
async function saveData(data) {
    try {
        data.lastUpdated = new Date().toISOString();
        await fs.writeFile(DATA_FILE, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error('Error saving data:', error);
        return false;
    }
}

// Generate unique ID
function generateId(prefix = 'ID') {
    return `${prefix}${Date.now()}${Math.floor(Math.random() * 1000)}`;
}

// API Routes

// Health check
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Products API
app.get('/api/products', async (req, res) => {
    try {
        const data = await loadData();
        res.json({ success: true, data: data.products });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.get('/api/products/:id', async (req, res) => {
    try {
        const data = await loadData();
        const product = data.products.find(p => p.id === req.params.id);
        if (product) {
            res.json({ success: true, data: product });
        } else {
            res.status(404).json({ success: false, error: 'Product not found' });
        }
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/products', async (req, res) => {
    try {
        const data = await loadData();
        const newProduct = {
            id: generateId('P'),
            hsnCode: generateId('P'),
            ...req.body,
            createdAt: new Date().toISOString()
        };
        data.products.push(newProduct);
        await saveData(data);
        res.json({ success: true, data: newProduct });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.put('/api/products/:id', async (req, res) => {
    try {
        const data = await loadData();
        const index = data.products.findIndex(p => p.id === req.params.id);
        if (index !== -1) {
            data.products[index] = { ...data.products[index], ...req.body, updatedAt: new Date().toISOString() };
            await saveData(data);
            res.json({ success: true, data: data.products[index] });
        } else {
            res.status(404).json({ success: false, error: 'Product not found' });
        }
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.delete('/api/products/:id', async (req, res) => {
    try {
        const data = await loadData();
        const index = data.products.findIndex(p => p.id === req.params.id);
        if (index !== -1) {
            const deletedProduct = data.products.splice(index, 1)[0];
            await saveData(data);
            res.json({ success: true, data: deletedProduct });
        } else {
            res.status(404).json({ success: false, error: 'Product not found' });
        }
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Vendors API
app.get('/api/vendors', async (req, res) => {
    try {
        const data = await loadData();
        res.json({ success: true, data: data.vendors });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/vendors', async (req, res) => {
    try {
        const data = await loadData();
        const newVendor = {
            id: generateId('V'),
            ...req.body,
            createdAt: new Date().toISOString()
        };
        data.vendors.push(newVendor);
        await saveData(data);
        res.json({ success: true, data: newVendor });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Customers API
app.get('/api/customers', async (req, res) => {
    try {
        const data = await loadData();
        res.json({ success: true, data: data.customers });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/customers', async (req, res) => {
    try {
        const data = await loadData();
        const newCustomer = {
            id: generateId('C'),
            ...req.body,
            createdAt: new Date().toISOString()
        };
        data.customers.push(newCustomer);
        await saveData(data);
        res.json({ success: true, data: newCustomer });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Purchases API
app.get('/api/purchases', async (req, res) => {
    try {
        const data = await loadData();
        res.json({ success: true, data: data.purchases });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/purchases', async (req, res) => {
    try {
        const data = await loadData();
        const newPurchase = {
            id: generateId('PUR'),
            ...req.body,
            createdAt: new Date().toISOString()
        };
        data.purchases.push(newPurchase);
        
        // Update product stock
        const product = data.products.find(p => p.id === newPurchase.productId);
        if (product) {
            product.currentStock = (product.currentStock || 0) + newPurchase.quantity;
        }
        
        await saveData(data);
        res.json({ success: true, data: newPurchase });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Sales API
app.get('/api/sales', async (req, res) => {
    try {
        const data = await loadData();
        res.json({ success: true, data: data.sales });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.post('/api/sales', async (req, res) => {
    try {
        const data = await loadData();
        const newSale = {
            id: generateId('SAL'),
            ...req.body,
            createdAt: new Date().toISOString()
        };
        
        // Check stock availability
        const product = data.products.find(p => p.id === newSale.productId);
        if (!product) {
            return res.status(400).json({ success: false, error: 'Product not found' });
        }
        
        if (product.currentStock < newSale.quantity) {
            return res.status(400).json({ success: false, error: 'Insufficient stock' });
        }
        
        // Update product stock
        product.currentStock -= newSale.quantity;
        data.sales.push(newSale);
        
        await saveData(data);
        res.json({ success: true, data: newSale });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Inventory API
app.get('/api/inventory', async (req, res) => {
    try {
        const data = await loadData();
        const inventory = data.products.map(product => {
            const stockValue = product.currentStock * product.costPrice;
            let status = 'Adequate';
            
            if (product.currentStock <= product.reorderLevel) {
                status = 'Critical';
            } else if (product.currentStock <= product.reorderLevel * 1.5) {
                status = 'Low Stock';
            }

            return {
                ...product,
                stockValue,
                status,
                lastUpdated: new Date().toISOString()
            };
        });
        
        res.json({ success: true, data: inventory });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.get('/api/inventory/alerts', async (req, res) => {
    try {
        const data = await loadData();
        const alerts = [];
        
        data.products.forEach(product => {
            if (product.currentStock <= product.reorderLevel) {
                alerts.push({
                    level: 'critical',
                    message: `${product.name} is critically low (${product.currentStock} units)`,
                    productId: product.id,
                    productName: product.name,
                    currentStock: product.currentStock,
                    reorderLevel: product.reorderLevel
                });
            } else if (product.currentStock <= product.reorderLevel * 1.5) {
                alerts.push({
                    level: 'warning',
                    message: `${product.name} is running low (${product.currentStock} units)`,
                    productId: product.id,
                    productName: product.name,
                    currentStock: product.currentStock,
                    reorderLevel: product.reorderLevel
                });
            }
        });
        
        res.json({ success: true, data: alerts });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Analytics API
app.get('/api/analytics', async (req, res) => {
    try {
        const data = await loadData();
        
        const totalSales = data.sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
        const totalPurchases = data.purchases.reduce((sum, purchase) => sum + purchase.totalAmount, 0);
        const totalInventoryValue = data.products.reduce((sum, product) => 
            sum + (product.currentStock * product.costPrice), 0);

        const criticalStock = data.products.filter(p => p.currentStock <= p.reorderLevel);
        const lowStock = data.products.filter(p => 
            p.currentStock > p.reorderLevel && p.currentStock <= p.reorderLevel * 1.5);

        const analytics = {
            totalProducts: data.products.length,
            totalCustomers: data.customers.length,
            totalVendors: data.vendors.length,
            totalSales,
            totalPurchases,
            totalInventoryValue,
            criticalStockCount: criticalStock.length,
            lowStockCount: lowStock.length,
            criticalStockItems: criticalStock,
            lowStockItems: lowStock
        };
        
        res.json({ success: true, data: analytics });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Search API
app.get('/api/search/products', async (req, res) => {
    try {
        const { q } = req.query;
        const data = await loadData();
        
        const results = data.products.filter(product =>
            product.name.toLowerCase().includes(q.toLowerCase()) ||
            product.category.toLowerCase().includes(q.toLowerCase()) ||
            product.supplier.toLowerCase().includes(q.toLowerCase())
        );
        
        res.json({ success: true, data: results });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Backup API
app.post('/api/backup', async (req, res) => {
    try {
        const backupData = req.body;
        const backupId = generateId('BACKUP');
        const backupFile = path.join(__dirname, 'data', `backup_${backupId}.json`);
        
        await fs.writeFile(backupFile, JSON.stringify(backupData, null, 2));
        res.json({ success: true, backupId, message: 'Backup created successfully' });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// System info API
app.get('/api/system/info', (req, res) => {
    res.json({
        success: true,
        data: {
            version: '1.0.0',
            name: 'Advanced Inventory Management System',
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            platform: process.platform,
            nodeVersion: process.version
        }
    });
});

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ success: false, error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ success: false, error: 'Endpoint not found' });
});

// Start server
async function startServer() {
    await ensureDataDirectory();
    
    app.listen(PORT, () => {
        console.log(`🚀 Inventory Management System running on http://localhost:${PORT}`);
        console.log(`📊 Dashboard: http://localhost:${PORT}`);
        console.log(`🔧 API: http://localhost:${PORT}/api`);
        console.log(`💾 Data stored in: ${DATA_FILE}`);
    });
}

startServer().catch(console.error);

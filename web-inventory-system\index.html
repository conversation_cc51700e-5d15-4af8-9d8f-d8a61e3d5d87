<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Inventory Management System</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
</head>
<body>
    <div class="app-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-boxes"></i> Inventory Pro</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active" data-screen="dashboard">
                    <a href="#"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li class="nav-item" data-screen="products">
                    <a href="#"><i class="fas fa-box"></i> Products</a>
                </li>
                <li class="nav-item" data-screen="vendors">
                    <a href="#"><i class="fas fa-truck"></i> Vendors</a>
                </li>
                <li class="nav-item" data-screen="customers">
                    <a href="#"><i class="fas fa-users"></i> Customers</a>
                </li>
                <li class="nav-item" data-screen="purchase">
                    <a href="#"><i class="fas fa-shopping-cart"></i> Purchase</a>
                </li>
                <li class="nav-item" data-screen="sales">
                    <a href="#"><i class="fas fa-chart-line"></i> Sales</a>
                </li>
                <li class="nav-item" data-screen="inventory">
                    <a href="#"><i class="fas fa-warehouse"></i> Inventory</a>
                </li>
                <li class="nav-item" data-screen="analytics">
                    <a href="#"><i class="fas fa-chart-bar"></i> Analytics</a>
                </li>
                <li class="nav-item" data-screen="new-entry">
                    <a href="#"><i class="fas fa-plus-circle"></i> New Entry</a>
                </li>
            </ul>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle"><i class="fas fa-bars"></i></button>
                    <h1 id="page-title">Dashboard</h1>
                </div>
                <div class="header-right">
                    <div class="search-box">
                        <input type="text" placeholder="Search..." id="global-search">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-count">4</span>
                        </button>
                    </div>
                    <div class="user-profile">
                        <img src="https://via.placeholder.com/40" alt="User">
                        <span>Admin User</span>
                    </div>
                </div>
            </header>

            <!-- Screen Content Container -->
            <div class="content-container">
                <!-- Dashboard Screen -->
                <div id="dashboard-screen" class="screen active">
                    <div class="dashboard-grid">
                        <!-- KPI Cards -->
                        <div class="kpi-cards">
                            <div class="kpi-card">
                                <div class="kpi-icon"><i class="fas fa-box"></i></div>
                                <div class="kpi-content">
                                    <h3 id="total-products">0</h3>
                                    <p>Total Products</p>
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-icon"><i class="fas fa-users"></i></div>
                                <div class="kpi-content">
                                    <h3 id="total-customers">0</h3>
                                    <p>Total Customers</p>
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-icon"><i class="fas fa-truck"></i></div>
                                <div class="kpi-content">
                                    <h3 id="total-vendors">0</h3>
                                    <p>Total Vendors</p>
                                </div>
                            </div>
                            <div class="kpi-card">
                                <div class="kpi-icon"><i class="fas fa-dollar-sign"></i></div>
                                <div class="kpi-content">
                                    <h3 id="total-sales">₹0</h3>
                                    <p>Total Sales</p>
                                </div>
                            </div>
                        </div>

                        <!-- Charts Section -->
                        <div class="charts-section">
                            <div class="chart-container">
                                <h3>Sales Trend</h3>
                                <canvas id="salesChart"></canvas>
                            </div>
                            <div class="chart-container">
                                <h3>Stock Levels</h3>
                                <canvas id="stockChart"></canvas>
                            </div>
                        </div>

                        <!-- Alerts Section -->
                        <div class="alerts-section">
                            <h3><i class="fas fa-exclamation-triangle"></i> Stock Alerts</h3>
                            <div id="stock-alerts" class="alerts-list">
                                <!-- Dynamic alerts will be loaded here -->
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="recent-activity">
                            <h3><i class="fas fa-clock"></i> Recent Activity</h3>
                            <div id="recent-activity-list" class="activity-list">
                                <!-- Dynamic activity will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Products Screen -->
                <div id="products-screen" class="screen">
                    <div class="screen-header">
                        <h2>Products Management</h2>
                        <button class="btn btn-primary" onclick="showAddProductModal()">
                            <i class="fas fa-plus"></i> Add Product
                        </button>
                    </div>
                    <div class="filters-section">
                        <input type="text" placeholder="Search products..." id="product-search">
                        <select id="category-filter">
                            <option value="">All Categories</option>
                            <option value="Electronics">Electronics</option>
                            <option value="Accessories">Accessories</option>
                            <option value="Components">Components</option>
                        </select>
                    </div>
                    <div class="table-container">
                        <table id="products-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>HSN Code</th>
                                    <th>Product Name</th>
                                    <th>Category</th>
                                    <th>Cost Price</th>
                                    <th>Selling Price</th>
                                    <th>Supplier</th>
                                    <th>Stock</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Other screens will be added dynamically -->
                <div id="vendors-screen" class="screen">
                    <div class="screen-header">
                        <h2>Vendors Management</h2>
                        <button class="btn btn-primary" onclick="showAddVendorModal()">
                            <i class="fas fa-plus"></i> Add Vendor
                        </button>
                    </div>
                    <div class="table-container">
                        <table id="vendors-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Vendor ID</th>
                                    <th>Name</th>
                                    <th>Contact Person</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Address</th>
                                    <th>Rating</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Customers Screen -->
                <div id="customers-screen" class="screen">
                    <div class="screen-header">
                        <h2>Customers Management</h2>
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">
                            <i class="fas fa-plus"></i> Add Customer
                        </button>
                    </div>
                    <div class="table-container">
                        <table id="customers-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Customer ID</th>
                                    <th>Name</th>
                                    <th>Contact Person</th>
                                    <th>Phone</th>
                                    <th>Email</th>
                                    <th>Type</th>
                                    <th>Credit Limit</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Purchase Screen -->
                <div id="purchase-screen" class="screen">
                    <div class="screen-header">
                        <h2>Purchase Management</h2>
                        <button class="btn btn-primary" onclick="showAddPurchaseModal()">
                            <i class="fas fa-plus"></i> New Purchase
                        </button>
                    </div>
                    <div class="table-container">
                        <table id="purchase-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Purchase ID</th>
                                    <th>Date</th>
                                    <th>Product</th>
                                    <th>Vendor</th>
                                    <th>Quantity</th>
                                    <th>Unit Cost</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Sales Screen -->
                <div id="sales-screen" class="screen">
                    <div class="screen-header">
                        <h2>Sales Management</h2>
                        <button class="btn btn-primary" onclick="showAddSaleModal()">
                            <i class="fas fa-plus"></i> New Sale
                        </button>
                    </div>
                    <div class="table-container">
                        <table id="sales-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Sale ID</th>
                                    <th>Date</th>
                                    <th>Customer</th>
                                    <th>Product</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Total Amount</th>
                                    <th>Payment Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Inventory Screen -->
                <div id="inventory-screen" class="screen">
                    <div class="screen-header">
                        <h2>Inventory Management</h2>
                        <button class="btn btn-secondary" onclick="refreshInventory()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                    <div class="inventory-summary">
                        <div class="summary-card critical">
                            <h4>Critical Stock</h4>
                            <span id="critical-count">0</span>
                        </div>
                        <div class="summary-card low">
                            <h4>Low Stock</h4>
                            <span id="low-count">0</span>
                        </div>
                        <div class="summary-card good">
                            <h4>Adequate Stock</h4>
                            <span id="adequate-count">0</span>
                        </div>
                    </div>
                    <div class="table-container">
                        <table id="inventory-table" class="data-table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Current Stock</th>
                                    <th>Reorder Level</th>
                                    <th>Max Level</th>
                                    <th>Stock Value</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Dynamic content -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Analytics Screen -->
                <div id="analytics-screen" class="screen">
                    <div class="screen-header">
                        <h2>Analytics & Reports</h2>
                        <div class="date-range-picker">
                            <input type="date" id="start-date">
                            <input type="date" id="end-date">
                            <button class="btn btn-secondary" onclick="updateAnalytics()">Update</button>
                        </div>
                    </div>
                    <div class="analytics-grid">
                        <div class="analytics-card">
                            <h3>Top Customers</h3>
                            <canvas id="topCustomersChart"></canvas>
                        </div>
                        <div class="analytics-card">
                            <h3>Product Performance</h3>
                            <canvas id="productPerformanceChart"></canvas>
                        </div>
                        <div class="analytics-card">
                            <h3>Vendor Analysis</h3>
                            <canvas id="vendorAnalysisChart"></canvas>
                        </div>
                        <div class="analytics-card">
                            <h3>Monthly Trends</h3>
                            <canvas id="monthlyTrendsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- New Entry Screen -->
                <div id="new-entry-screen" class="screen">
                    <div class="screen-header">
                        <h2>Quick Entry</h2>
                    </div>
                    <div class="entry-options">
                        <div class="entry-card" onclick="showAddProductModal()">
                            <i class="fas fa-box"></i>
                            <h3>Add Product</h3>
                            <p>Add new products to inventory</p>
                        </div>
                        <div class="entry-card" onclick="showAddCustomerModal()">
                            <i class="fas fa-user-plus"></i>
                            <h3>Add Customer</h3>
                            <p>Register new customers</p>
                        </div>
                        <div class="entry-card" onclick="showAddVendorModal()">
                            <i class="fas fa-truck"></i>
                            <h3>Add Vendor</h3>
                            <p>Add new suppliers</p>
                        </div>
                        <div class="entry-card" onclick="showAddPurchaseModal()">
                            <i class="fas fa-shopping-cart"></i>
                            <h3>Record Purchase</h3>
                            <p>Add purchase transactions</p>
                        </div>
                        <div class="entry-card" onclick="showAddSaleModal()">
                            <i class="fas fa-cash-register"></i>
                            <h3>Record Sale</h3>
                            <p>Add sales transactions</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modal Container -->
    <div id="modal-container"></div>

    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner"></div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/data.js"></script>
    <script src="js/api.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/modals.js"></script>
    <script src="js/app.js"></script>
</body>
</html>

// Simple PostgreSQL Server
const express = require('express');
const cors = require('cors');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

console.log('🚀 Starting PostgreSQL server...');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// PostgreSQL connection
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
});

// Test database connection
pool.on('connect', () => {
    console.log('✅ Connected to PostgreSQL database');
});

pool.on('error', (err) => {
    console.error('❌ PostgreSQL connection error:', err);
});

// Health check
app.get('/api/health', async (req, res) => {
    try {
        await pool.query('SELECT 1');
        res.json({ 
            status: 'ok', 
            timestamp: new Date().toISOString(),
            database: 'connected'
        });
    } catch (error) {
        res.status(500).json({ 
            status: 'error', 
            timestamp: new Date().toISOString(),
            database: 'disconnected',
            error: error.message
        });
    }
});

// Products API
app.get('/api/products', async (req, res) => {
    try {
        const query = `
            SELECT 
                p.id,
                p.product_code as "hsnCode",
                p.name,
                c.name as category,
                p.cost_price as "costPrice",
                p.selling_price as "sellingPrice",
                v.name as supplier,
                p.reorder_level as "reorderLevel",
                p.max_stock_level as "maxStockLevel",
                p.current_stock as "currentStock",
                p.created_at as "createdAt"
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN vendors v ON p.vendor_id = v.id
            WHERE p.is_active = TRUE
            ORDER BY p.name
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Products API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Vendors API
app.get('/api/vendors', async (req, res) => {
    try {
        const query = `
            SELECT 
                id,
                vendor_code as "vendorCode",
                name,
                contact_person as "contactPerson",
                phone,
                email,
                address,
                city,
                state,
                payment_terms as "paymentTerms",
                rating,
                created_at as "createdAt"
            FROM vendors 
            WHERE is_active = TRUE 
            ORDER BY name
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Vendors API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Customers API
app.get('/api/customers', async (req, res) => {
    try {
        const query = `
            SELECT 
                id,
                customer_code as "customerCode",
                name,
                contact_person as "contactPerson",
                phone,
                email,
                address,
                city,
                state,
                customer_type as "type",
                credit_limit as "creditLimit",
                current_balance as "currentBalance",
                created_at as "createdAt"
            FROM customers 
            WHERE is_active = TRUE 
            ORDER BY name
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Customers API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Sales API
app.get('/api/sales', async (req, res) => {
    try {
        const query = `
            SELECT 
                s.id,
                s.sale_code as "saleCode",
                s.sale_date as "date",
                s.customer_id as "customerId",
                c.name as "customerName",
                s.product_id as "productId",
                p.name as "productName",
                s.quantity,
                s.unit_price as "unitPrice",
                s.final_amount as "totalAmount",
                s.payment_status as "paymentStatus",
                s.payment_method as "paymentMethod",
                s.payment_date as "paymentDate",
                s.created_at as "createdAt"
            FROM sales s
            JOIN customers c ON s.customer_id = c.id
            JOIN products p ON s.product_id = p.id
            ORDER BY s.sale_date DESC
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Sales API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Purchases API
app.get('/api/purchases', async (req, res) => {
    try {
        const query = `
            SELECT 
                p.id,
                p.purchase_code as "purchaseCode",
                p.purchase_date as "date",
                p.product_id as "productId",
                pr.name as "productName",
                v.name as "vendor",
                p.quantity,
                p.unit_cost as "unitCost",
                p.final_amount as "totalAmount",
                p.status,
                p.invoice_number as "invoiceNumber",
                p.created_at as "createdAt"
            FROM purchases p
            JOIN vendors v ON p.vendor_id = v.id
            JOIN products pr ON p.product_id = pr.id
            ORDER BY p.purchase_date DESC
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Purchases API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Inventory API
app.get('/api/inventory', async (req, res) => {
    try {
        const query = `
            SELECT 
                product_code as "hsnCode",
                name,
                category_name as "category",
                current_stock as "currentStock",
                reorder_level as "reorderLevel",
                max_stock_level as "maxStockLevel",
                cost_price as "costPrice",
                stock_value as "stockValue",
                stock_status as "status",
                vendor_name as "supplier",
                vendor_phone as "vendorPhone",
                last_updated as "lastUpdated"
            FROM inventory_status
            ORDER BY 
                CASE stock_status
                    WHEN 'Critical' THEN 1
                    WHEN 'Low Stock' THEN 2
                    WHEN 'Adequate' THEN 3
                    WHEN 'Overstocked' THEN 4
                END,
                name
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        console.error('Inventory API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Analytics API
app.get('/api/analytics', async (req, res) => {
    try {
        const queries = {
            totalProducts: 'SELECT COUNT(*) as count FROM products WHERE is_active = TRUE',
            totalCustomers: 'SELECT COUNT(*) as count FROM customers WHERE is_active = TRUE',
            totalVendors: 'SELECT COUNT(*) as count FROM vendors WHERE is_active = TRUE',
            totalSales: 'SELECT COALESCE(SUM(final_amount), 0) as total FROM sales',
            totalPurchases: 'SELECT COALESCE(SUM(final_amount), 0) as total FROM purchases',
            totalInventoryValue: 'SELECT COALESCE(SUM(stock_value), 0) as total FROM inventory_status',
            criticalStock: 'SELECT COUNT(*) as count FROM inventory_status WHERE stock_status = \'Critical\'',
            lowStock: 'SELECT COUNT(*) as count FROM inventory_status WHERE stock_status = \'Low Stock\''
        };

        const results = {};
        
        for (const [key, query] of Object.entries(queries)) {
            const result = await pool.query(query);
            if (key.includes('total') || key.includes('Total')) {
                results[key] = parseFloat(result.rows[0].total || result.rows[0].count || 0);
            } else {
                results[key + 'Count'] = parseInt(result.rows[0].count || 0);
            }
        }
        
        res.json({ success: true, data: results });
    } catch (error) {
        console.error('Analytics API error:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ success: false, error: 'Something went wrong!' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ success: false, error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 PostgreSQL Inventory System running on http://localhost:${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}`);
    console.log(`🔧 API Health: http://localhost:${PORT}/api/health`);
    console.log(`🗄️  Database: PostgreSQL (${process.env.DB_NAME || 'inventory_management'})`);
    console.log(`📋 pgAdmin4: Access your database through pgAdmin4`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🔄 Shutting down gracefully...');
    await pool.end();
    console.log('✅ Database connections closed');
    process.exit(0);
});

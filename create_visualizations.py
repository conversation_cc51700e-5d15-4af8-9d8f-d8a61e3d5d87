import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import numpy as np

def create_visualizations():
    """
    Create visualizations for the inventory management system
    """
    # Set style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Read the Excel file
    file_path = "inventory management software by deepak eduworld.xlsx"
    
    try:
        # Create figure with subplots
        fig = plt.figure(figsize=(20, 16))
        fig.suptitle('Inventory Management System - Business Analytics Dashboard', fontsize=20, fontweight='bold')
        
        # 1. Product Price Analysis
        ax1 = plt.subplot(2, 3, 1)
        products_df = pd.read_excel(file_path, sheet_name='Products', header=3)
        products_df = products_df.dropna(how='all')
        
        if len(products_df) > 0:
            # Clean the data
            products_clean = products_df[['Unnamed: 4', 'Unnamed: 5', 'Unnamed: 6']].copy()
            products_clean.columns = ['Product', 'Cost', 'Price']
            products_clean = products_clean.dropna()
            
            # Convert to numeric
            products_clean['Cost'] = pd.to_numeric(products_clean['Cost'], errors='coerce')
            products_clean['Price'] = pd.to_numeric(products_clean['Price'], errors='coerce')
            products_clean = products_clean.dropna()
            
            if len(products_clean) > 0:
                # Create bar chart
                x_pos = np.arange(len(products_clean))
                ax1.bar(x_pos - 0.2, products_clean['Cost'], 0.4, label='Cost', alpha=0.8)
                ax1.bar(x_pos + 0.2, products_clean['Price'], 0.4, label='Selling Price', alpha=0.8)
                ax1.set_xlabel('Products')
                ax1.set_ylabel('Amount ($)')
                ax1.set_title('Product Cost vs Selling Price')
                ax1.set_xticks(x_pos)
                ax1.set_xticklabels(products_clean['Product'], rotation=45, ha='right')
                ax1.legend()
                ax1.grid(True, alpha=0.3)
        
        # 2. Inventory Stock Levels
        ax2 = plt.subplot(2, 3, 2)
        inventory_df = pd.read_excel(file_path, sheet_name='Inventory', header=3)
        inventory_df = inventory_df.dropna(how='all')
        
        if len(inventory_df) > 0:
            # Clean the data
            inventory_clean = inventory_df[['Unnamed: 4', 'Unnamed: 8']].copy()
            inventory_clean.columns = ['Product', 'Stock']
            inventory_clean = inventory_clean.dropna()
            
            # Convert stock to numeric
            inventory_clean['Stock'] = pd.to_numeric(inventory_clean['Stock'], errors='coerce')
            inventory_clean = inventory_clean.dropna()
            
            if len(inventory_clean) > 0:
                # Color code based on stock levels
                colors = ['red' if stock <= 5 else 'orange' if stock <= 15 else 'green' 
                         for stock in inventory_clean['Stock']]
                
                bars = ax2.bar(range(len(inventory_clean)), inventory_clean['Stock'], color=colors, alpha=0.7)
                ax2.set_xlabel('Products')
                ax2.set_ylabel('Stock Units')
                ax2.set_title('Current Inventory Stock Levels')
                ax2.set_xticks(range(len(inventory_clean)))
                ax2.set_xticklabels(inventory_clean['Product'], rotation=45, ha='right')
                
                # Add value labels on bars
                for bar, value in zip(bars, inventory_clean['Stock']):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                            f'{int(value)}', ha='center', va='bottom')
                
                ax2.grid(True, alpha=0.3)
                ax2.axhline(y=5, color='red', linestyle='--', alpha=0.5, label='Critical Level')
                ax2.legend()
        
        # 3. Vendor Distribution
        ax3 = plt.subplot(2, 3, 3)
        vendors_df = pd.read_excel(file_path, sheet_name='Vendors', header=3)
        vendors_df = vendors_df.dropna(how='all')
        
        if len(vendors_df) > 0:
            vendor_clean = vendors_df[['Unnamed: 5']].copy()
            vendor_clean.columns = ['Vendor']
            vendor_clean = vendor_clean.dropna()
            
            if len(vendor_clean) > 0:
                vendor_counts = vendor_clean['Vendor'].value_counts()
                
                # Create pie chart
                colors = plt.cm.Set3(np.linspace(0, 1, len(vendor_counts)))
                wedges, texts, autotexts = ax3.pie(vendor_counts.values, labels=vendor_counts.index, 
                                                  autopct='%1.1f%%', colors=colors, startangle=90)
                ax3.set_title('Vendor Distribution by Product Count')
        
        # 4. Sales by Customer
        ax4 = plt.subplot(2, 3, 4)
        sales_df = pd.read_excel(file_path, sheet_name='Sales', header=3)
        sales_df = sales_df.dropna(how='all')
        
        if len(sales_df) > 0:
            sales_clean = sales_df[['Unnamed: 4', 'Unnamed: 11']].copy()
            sales_clean.columns = ['Customer', 'Amount']
            sales_clean = sales_clean.dropna()
            
            # Convert amount to numeric
            sales_clean['Amount'] = pd.to_numeric(sales_clean['Amount'], errors='coerce')
            sales_clean = sales_clean.dropna()
            
            if len(sales_clean) > 0:
                customer_sales = sales_clean.groupby('Customer')['Amount'].sum().sort_values(ascending=True)
                
                bars = ax4.barh(range(len(customer_sales)), customer_sales.values, alpha=0.8)
                ax4.set_xlabel('Total Sales Amount ($)')
                ax4.set_ylabel('Customers')
                ax4.set_title('Sales by Customer')
                ax4.set_yticks(range(len(customer_sales)))
                ax4.set_yticklabels(customer_sales.index)
                
                # Add value labels
                for i, (bar, value) in enumerate(zip(bars, customer_sales.values)):
                    ax4.text(value + max(customer_sales.values) * 0.01, i, 
                            f'${value:,.0f}', va='center', ha='left')
                
                ax4.grid(True, alpha=0.3, axis='x')
        
        # 5. Purchase vs Sales Timeline
        ax5 = plt.subplot(2, 3, 5)
        purchase_df = pd.read_excel(file_path, sheet_name='Purchase', header=3)
        purchase_df = purchase_df.dropna(how='all')
        
        if len(purchase_df) > 0 and len(sales_df) > 0:
            # Process purchase data
            purchase_clean = purchase_df[['Unnamed: 6', 'Unnamed: 9']].copy()
            purchase_clean.columns = ['Date', 'Amount']
            purchase_clean = purchase_clean.dropna()
            purchase_clean['Amount'] = pd.to_numeric(purchase_clean['Amount'], errors='coerce')
            purchase_clean['Date'] = pd.to_datetime(purchase_clean['Date'], errors='coerce')
            purchase_clean = purchase_clean.dropna()
            
            # Process sales data
            sales_timeline = sales_df[['Unnamed: 7', 'Unnamed: 11']].copy()
            sales_timeline.columns = ['Date', 'Amount']
            sales_timeline = sales_timeline.dropna()
            sales_timeline['Amount'] = pd.to_numeric(sales_timeline['Amount'], errors='coerce')
            sales_timeline['Date'] = pd.to_datetime(sales_timeline['Date'], errors='coerce')
            sales_timeline = sales_timeline.dropna()
            
            if len(purchase_clean) > 0 and len(sales_timeline) > 0:
                # Group by date
                purchase_daily = purchase_clean.groupby('Date')['Amount'].sum()
                sales_daily = sales_timeline.groupby('Date')['Amount'].sum()
                
                # Plot lines
                ax5.plot(purchase_daily.index, purchase_daily.values, 'o-', label='Purchases', linewidth=2, markersize=6)
                ax5.plot(sales_daily.index, sales_daily.values, 's-', label='Sales', linewidth=2, markersize=6)
                ax5.set_xlabel('Date')
                ax5.set_ylabel('Amount ($)')
                ax5.set_title('Purchase vs Sales Timeline')
                ax5.legend()
                ax5.grid(True, alpha=0.3)
                ax5.tick_params(axis='x', rotation=45)
        
        # 6. Stock Alert Summary
        ax6 = plt.subplot(2, 3, 6)
        if len(inventory_clean) > 0:
            # Categorize stock levels
            critical = len(inventory_clean[inventory_clean['Stock'] <= 5])
            low = len(inventory_clean[(inventory_clean['Stock'] > 5) & (inventory_clean['Stock'] <= 15)])
            healthy = len(inventory_clean[inventory_clean['Stock'] > 15])
            
            categories = ['Critical\n(≤5)', 'Low\n(6-15)', 'Healthy\n(>15)']
            counts = [critical, low, healthy]
            colors = ['red', 'orange', 'green']
            
            bars = ax6.bar(categories, counts, color=colors, alpha=0.7)
            ax6.set_ylabel('Number of Products')
            ax6.set_title('Stock Level Distribution')
            
            # Add value labels
            for bar, count in zip(bars, counts):
                ax6.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                        f'{count}', ha='center', va='bottom', fontweight='bold')
            
            ax6.grid(True, alpha=0.3, axis='y')
        
        # Adjust layout and save
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        
        # Save the plot
        plt.savefig('inventory_dashboard.png', dpi=300, bbox_inches='tight')
        print("✅ Dashboard visualization saved as 'inventory_dashboard.png'")
        
        # Show the plot
        plt.show()
        
    except Exception as e:
        print(f"Error creating visualizations: {str(e)}")

if __name__ == "__main__":
    create_visualizations()

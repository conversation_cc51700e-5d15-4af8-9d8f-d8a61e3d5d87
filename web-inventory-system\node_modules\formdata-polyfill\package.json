{"name": "formdata-polyfill", "version": "4.0.10", "description": "HTML5 `FormData` for Browsers and Node.", "type": "module", "main": "formdata.min.js", "scripts": {"build": "node build.js", "test": "node test/test-esm.js", "test-wpt": "node --experimental-loader ./test/http-loader.js ./test/test-wpt-in-node.js", "test-polyfill": "php -S localhost:4445 & open http://localhost:4445/test/test-polyfill.html"}, "repository": {"type": "git", "url": "git+https://<EMAIL>/jimmywarting/FormData.git"}, "files": ["esm.min.js", "esm.min.d.ts", "FormData.js", "formdata-to-blob.js", "formdata.min.js", "README.md"], "engines": {"node": ">=12.20.0"}, "keywords": ["formdata", "fetch", "node-fetch", "html5", "browser", "polyfill"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/jimmywarting/FormData/issues"}, "homepage": "https://github.com/jimmywarting/FormData#readme", "dependencies": {"fetch-blob": "^3.1.2"}, "devDependencies": {"@types/google-closure-compiler": "^0.0.19", "@types/node": "^16.7.10", "google-closure-compiler": "^20210808.0.0"}}
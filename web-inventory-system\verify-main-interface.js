// Verify Main Interface is Working with All 14 Tables
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

async function verifyMainInterface() {
    console.log('🔍 Verifying Main Interface with All 14 Tables...\n');

    try {
        await pool.query('SELECT 1');
        console.log('✅ Database Connected\n');

        // Check all 14 tables have data
        const tables = [
            'vendor', 'customer', 'part', 'unit', '"user"', 'user_role', 'tax',
            'indent_request', 'purchase_order', 'goods_receipt', 'sales_invoice',
            'accounts_payable', 'accounts_receivable', 'stock_update'
        ];

        console.log('📊 Table Data Verification:');
        console.log('===========================');
        
        let totalRecords = 0;
        let tablesWithData = 0;
        
        for (const table of tables) {
            try {
                const result = await pool.query(`SELECT COUNT(*) FROM ${table}`);
                const count = parseInt(result.rows[0].count);
                totalRecords += count;
                
                if (count > 0) {
                    tablesWithData++;
                    console.log(`✅ ${table.replace('"', '').replace('"', '').toUpperCase()}: ${count} records`);
                } else {
                    console.log(`⚠️  ${table.replace('"', '').replace('"', '').toUpperCase()}: ${count} records (EMPTY)`);
                }
            } catch (error) {
                console.log(`❌ ${table.toUpperCase()}: Error - ${error.message}`);
            }
        }

        console.log(`\n📈 Summary: ${tablesWithData}/14 tables have data, ${totalRecords} total records`);

        // Test API endpoints
        console.log('\n🔌 API Endpoint Verification:');
        console.log('==============================');
        
        const fetch = (await import('node-fetch')).default;
        const apiEndpoints = [
            'vendors', 'customers', 'parts', 'units', 'users', 'user-roles', 'taxes',
            'indents', 'purchase-orders', 'goods-receipts', 'sales-invoices',
            'accounts-payable', 'accounts-receivable', 'stock-updates'
        ];

        let workingAPIs = 0;
        for (const endpoint of apiEndpoints) {
            try {
                const response = await fetch(`http://localhost:3000/api/${endpoint}`);
                const result = await response.json();
                
                if (result.success) {
                    workingAPIs++;
                    console.log(`✅ /api/${endpoint}: ${result.data.length} records`);
                } else {
                    console.log(`❌ /api/${endpoint}: ${result.error}`);
                }
            } catch (error) {
                console.log(`❌ /api/${endpoint}: ${error.message}`);
            }
        }

        console.log(`\n🌐 API Summary: ${workingAPIs}/14 endpoints working`);

        // Sample data verification
        console.log('\n📋 Sample Data Check:');
        console.log('======================');
        
        // Check vendor data
        const vendorResult = await pool.query('SELECT vendor_name, city FROM vendor LIMIT 3');
        console.log('✅ Sample Vendors:');
        vendorResult.rows.forEach(row => {
            console.log(`   • ${row.vendor_name} (${row.city})`);
        });

        // Check customer data
        const customerResult = await pool.query('SELECT customer_name, city FROM customer LIMIT 3');
        console.log('✅ Sample Customers:');
        customerResult.rows.forEach(row => {
            console.log(`   • ${row.customer_name} (${row.city})`);
        });

        // Check part data
        const partResult = await pool.query('SELECT part_desc, pur_price, sell_price FROM part LIMIT 3');
        console.log('✅ Sample Parts:');
        partResult.rows.forEach(row => {
            console.log(`   • ${row.part_desc} (₹${row.pur_price} → ₹${row.sell_price})`);
        });

        console.log('\n🎉 Main Interface Verification Complete!');
        console.log('\n📋 What You Can Do Now:');
        console.log('========================');
        console.log('✅ Open http://localhost:3000 in your browser');
        console.log('✅ Navigate through all 14 table sections in the sidebar');
        console.log('✅ View existing data in tables');
        console.log('✅ Add new records using the "Add New" buttons');
        console.log('✅ Edit existing records using the edit buttons');
        console.log('✅ Delete records using the delete buttons');
        console.log('✅ Search and filter data in each table');
        console.log('✅ View dashboard statistics');
        
        console.log('\n🚀 Your complete 14-table inventory management system is ready!');

    } catch (error) {
        console.error('❌ Verification failed:', error);
    } finally {
        await pool.end();
    }
}

verifyMainInterface();

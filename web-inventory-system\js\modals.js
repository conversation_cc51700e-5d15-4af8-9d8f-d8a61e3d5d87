// Modal Management System
class ModalManager {
    constructor() {
        this.modalContainer = document.getElementById('modal-container');
    }

    showModal(title, content, actions = []) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="close-btn" onclick="modalManager.closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-actions">
                    ${actions.map(action => `
                        <button class="btn ${action.class}" onclick="${action.onclick}">
                            ${action.text}
                        </button>
                    `).join('')}
                </div>
            </div>
        `;

        this.modalContainer.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });

        return modal;
    }

    closeModal() {
        this.modalContainer.innerHTML = '';
    }

    showProductModal(product = null) {
        const isEdit = !!product;
        const title = isEdit ? 'Edit Product' : 'Add New Product';
        
        const vendors = dataManager.getVendors();
        const categories = ['Electronics', 'Accessories', 'Components', 'Storage'];

        const content = `
            <form id="product-form">
                <div class="form-group">
                    <label for="product-name">Product Name *</label>
                    <input type="text" id="product-name" name="name" value="${product?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="product-category">Category *</label>
                    <select id="product-category" name="category" required>
                        <option value="">Select Category</option>
                        ${categories.map(cat => `
                            <option value="${cat}" ${product?.category === cat ? 'selected' : ''}>${cat}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="cost-price">Cost Price *</label>
                    <input type="number" id="cost-price" name="costPrice" value="${product?.costPrice || ''}" required>
                </div>
                <div class="form-group">
                    <label for="selling-price">Selling Price *</label>
                    <input type="number" id="selling-price" name="sellingPrice" value="${product?.sellingPrice || ''}" required>
                </div>
                <div class="form-group">
                    <label for="supplier">Supplier *</label>
                    <select id="supplier" name="supplier" required>
                        <option value="">Select Supplier</option>
                        ${vendors.map(vendor => `
                            <option value="${vendor.name}" ${product?.supplier === vendor.name ? 'selected' : ''}>${vendor.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="reorder-level">Reorder Level *</label>
                    <input type="number" id="reorder-level" name="reorderLevel" value="${product?.reorderLevel || ''}" required>
                </div>
                <div class="form-group">
                    <label for="max-stock">Max Stock Level *</label>
                    <input type="number" id="max-stock" name="maxStockLevel" value="${product?.maxStockLevel || ''}" required>
                </div>
                ${!isEdit ? `
                <div class="form-group">
                    <label for="initial-stock">Initial Stock</label>
                    <input type="number" id="initial-stock" name="currentStock" value="0">
                </div>
                ` : ''}
            </form>
        `;

        const actions = [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                onclick: 'modalManager.closeModal()'
            },
            {
                text: isEdit ? 'Update Product' : 'Add Product',
                class: 'btn-primary',
                onclick: `modalManager.${isEdit ? 'updateProduct' : 'saveProduct'}('${product?.id || ''}')`
            }
        ];

        this.showModal(title, content, actions);
    }

    showVendorModal(vendor = null) {
        const isEdit = !!vendor;
        const title = isEdit ? 'Edit Vendor' : 'Add New Vendor';

        const content = `
            <form id="vendor-form">
                <div class="form-group">
                    <label for="vendor-name">Vendor Name *</label>
                    <input type="text" id="vendor-name" name="name" value="${vendor?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="contact-person">Contact Person *</label>
                    <input type="text" id="contact-person" name="contactPerson" value="${vendor?.contactPerson || ''}" required>
                </div>
                <div class="form-group">
                    <label for="vendor-phone">Phone *</label>
                    <input type="tel" id="vendor-phone" name="phone" value="${vendor?.phone || ''}" required>
                </div>
                <div class="form-group">
                    <label for="vendor-email">Email *</label>
                    <input type="email" id="vendor-email" name="email" value="${vendor?.email || ''}" required>
                </div>
                <div class="form-group">
                    <label for="vendor-address">Address *</label>
                    <textarea id="vendor-address" name="address" rows="3" required>${vendor?.address || ''}</textarea>
                </div>
                <div class="form-group">
                    <label for="payment-terms">Payment Terms</label>
                    <select id="payment-terms" name="paymentTerms">
                        <option value="15 Days" ${vendor?.paymentTerms === '15 Days' ? 'selected' : ''}>15 Days</option>
                        <option value="30 Days" ${vendor?.paymentTerms === '30 Days' ? 'selected' : ''}>30 Days</option>
                        <option value="45 Days" ${vendor?.paymentTerms === '45 Days' ? 'selected' : ''}>45 Days</option>
                        <option value="60 Days" ${vendor?.paymentTerms === '60 Days' ? 'selected' : ''}>60 Days</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="vendor-rating">Rating</label>
                    <input type="number" id="vendor-rating" name="rating" min="1" max="5" step="0.1" value="${vendor?.rating || '4.0'}">
                </div>
            </form>
        `;

        const actions = [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                onclick: 'modalManager.closeModal()'
            },
            {
                text: isEdit ? 'Update Vendor' : 'Add Vendor',
                class: 'btn-primary',
                onclick: `modalManager.${isEdit ? 'updateVendor' : 'saveVendor'}('${vendor?.id || ''}')`
            }
        ];

        this.showModal(title, content, actions);
    }

    showCustomerModal(customer = null) {
        const isEdit = !!customer;
        const title = isEdit ? 'Edit Customer' : 'Add New Customer';

        const content = `
            <form id="customer-form">
                <div class="form-group">
                    <label for="customer-name">Customer Name *</label>
                    <input type="text" id="customer-name" name="name" value="${customer?.name || ''}" required>
                </div>
                <div class="form-group">
                    <label for="customer-contact">Contact Person *</label>
                    <input type="text" id="customer-contact" name="contactPerson" value="${customer?.contactPerson || ''}" required>
                </div>
                <div class="form-group">
                    <label for="customer-phone">Phone *</label>
                    <input type="tel" id="customer-phone" name="phone" value="${customer?.phone || ''}" required>
                </div>
                <div class="form-group">
                    <label for="customer-email">Email *</label>
                    <input type="email" id="customer-email" name="email" value="${customer?.email || ''}" required>
                </div>
                <div class="form-group">
                    <label for="customer-address">Address *</label>
                    <textarea id="customer-address" name="address" rows="3" required>${customer?.address || ''}</textarea>
                </div>
                <div class="form-group">
                    <label for="customer-type">Customer Type *</label>
                    <select id="customer-type" name="type" required>
                        <option value="">Select Type</option>
                        <option value="B2B" ${customer?.type === 'B2B' ? 'selected' : ''}>B2B (Business)</option>
                        <option value="B2C" ${customer?.type === 'B2C' ? 'selected' : ''}>B2C (Consumer)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="credit-limit">Credit Limit</label>
                    <input type="number" id="credit-limit" name="creditLimit" value="${customer?.creditLimit || '100000'}">
                </div>
            </form>
        `;

        const actions = [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                onclick: 'modalManager.closeModal()'
            },
            {
                text: isEdit ? 'Update Customer' : 'Add Customer',
                class: 'btn-primary',
                onclick: `modalManager.${isEdit ? 'updateCustomer' : 'saveCustomer'}('${customer?.id || ''}')`
            }
        ];

        this.showModal(title, content, actions);
    }

    showPurchaseModal() {
        const products = dataManager.getProducts();
        const vendors = dataManager.getVendors();

        const content = `
            <form id="purchase-form">
                <div class="form-group">
                    <label for="purchase-date">Date *</label>
                    <input type="date" id="purchase-date" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                </div>
                <div class="form-group">
                    <label for="purchase-product">Product *</label>
                    <select id="purchase-product" name="productId" required onchange="modalManager.updatePurchaseProductInfo()">
                        <option value="">Select Product</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-name="${product.name}" data-cost="${product.costPrice}" data-supplier="${product.supplier}">
                                ${product.name}
                            </option>
                        `).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="purchase-vendor">Vendor *</label>
                    <select id="purchase-vendor" name="vendor" required>
                        <option value="">Select Vendor</option>
                        ${vendors.map(vendor => `
                            <option value="${vendor.name}">${vendor.name}</option>
                        `).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="purchase-quantity">Quantity *</label>
                    <input type="number" id="purchase-quantity" name="quantity" min="1" required onchange="modalManager.calculatePurchaseTotal()">
                </div>
                <div class="form-group">
                    <label for="purchase-cost">Unit Cost *</label>
                    <input type="number" id="purchase-cost" name="unitCost" step="0.01" required onchange="modalManager.calculatePurchaseTotal()">
                </div>
                <div class="form-group">
                    <label for="purchase-total">Total Amount</label>
                    <input type="number" id="purchase-total" name="totalAmount" readonly>
                </div>
                <div class="form-group">
                    <label for="purchase-status">Status</label>
                    <select id="purchase-status" name="status">
                        <option value="Pending">Pending</option>
                        <option value="Received">Received</option>
                        <option value="Partial">Partial</option>
                    </select>
                </div>
            </form>
        `;

        const actions = [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                onclick: 'modalManager.closeModal()'
            },
            {
                text: 'Add Purchase',
                class: 'btn-primary',
                onclick: 'modalManager.savePurchase()'
            }
        ];

        this.showModal('New Purchase', content, actions);
    }

    showSaleModal() {
        const products = dataManager.getProducts();
        const customers = dataManager.getCustomers();

        const content = `
            <form id="sale-form">
                <div class="form-group">
                    <label for="sale-date">Date *</label>
                    <input type="date" id="sale-date" name="date" value="${new Date().toISOString().split('T')[0]}" required>
                </div>
                <div class="form-group">
                    <label for="sale-customer">Customer *</label>
                    <select id="sale-customer" name="customerId" required onchange="modalManager.updateSaleCustomerInfo()">
                        <option value="">Select Customer</option>
                        ${customers.map(customer => `
                            <option value="${customer.id}" data-name="${customer.name}">
                                ${customer.name}
                            </option>
                        `).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="sale-product">Product *</label>
                    <select id="sale-product" name="productId" required onchange="modalManager.updateSaleProductInfo()">
                        <option value="">Select Product</option>
                        ${products.map(product => `
                            <option value="${product.id}" data-name="${product.name}" data-price="${product.sellingPrice}" data-stock="${product.currentStock}">
                                ${product.name} (Stock: ${product.currentStock})
                            </option>
                        `).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="sale-quantity">Quantity *</label>
                    <input type="number" id="sale-quantity" name="quantity" min="1" required onchange="modalManager.calculateSaleTotal()">
                </div>
                <div class="form-group">
                    <label for="sale-price">Unit Price *</label>
                    <input type="number" id="sale-price" name="unitPrice" step="0.01" required onchange="modalManager.calculateSaleTotal()">
                </div>
                <div class="form-group">
                    <label for="sale-total">Total Amount</label>
                    <input type="number" id="sale-total" name="totalAmount" readonly>
                </div>
                <div class="form-group">
                    <label for="payment-status">Payment Status</label>
                    <select id="payment-status" name="paymentStatus">
                        <option value="Pending">Pending</option>
                        <option value="Paid">Paid</option>
                        <option value="Partial">Partial</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="payment-method">Payment Method</label>
                    <select id="payment-method" name="paymentMethod">
                        <option value="Cash">Cash</option>
                        <option value="Card">Card</option>
                        <option value="UPI">UPI</option>
                        <option value="Bank Transfer">Bank Transfer</option>
                        <option value="Credit">Credit</option>
                    </select>
                </div>
            </form>
        `;

        const actions = [
            {
                text: 'Cancel',
                class: 'btn-secondary',
                onclick: 'modalManager.closeModal()'
            },
            {
                text: 'Add Sale',
                class: 'btn-primary',
                onclick: 'modalManager.saveSale()'
            }
        ];

        this.showModal('New Sale', content, actions);
    }

    // Form submission methods
    saveProduct(productId = null) {
        const form = document.getElementById('product-form');
        const formData = new FormData(form);
        const productData = Object.fromEntries(formData.entries());
        
        // Convert numeric fields
        productData.costPrice = parseFloat(productData.costPrice);
        productData.sellingPrice = parseFloat(productData.sellingPrice);
        productData.reorderLevel = parseInt(productData.reorderLevel);
        productData.maxStockLevel = parseInt(productData.maxStockLevel);
        productData.currentStock = parseInt(productData.currentStock || 0);

        if (productId) {
            dataManager.updateProduct(productId, productData);
        } else {
            dataManager.addProduct(productData);
        }

        this.closeModal();
        app.loadProducts();
        app.updateNotificationCount();
    }

    updateProduct(productId) {
        this.saveProduct(productId);
    }

    saveVendor(vendorId = null) {
        const form = document.getElementById('vendor-form');
        const formData = new FormData(form);
        const vendorData = Object.fromEntries(formData.entries());
        
        vendorData.rating = parseFloat(vendorData.rating);

        if (vendorId) {
            // Update logic would go here
        } else {
            dataManager.addVendor(vendorData);
        }

        this.closeModal();
        app.loadVendors();
    }

    saveCustomer(customerId = null) {
        const form = document.getElementById('customer-form');
        const formData = new FormData(form);
        const customerData = Object.fromEntries(formData.entries());
        
        customerData.creditLimit = parseFloat(customerData.creditLimit);

        if (customerId) {
            // Update logic would go here
        } else {
            dataManager.addCustomer(customerData);
        }

        this.closeModal();
        app.loadCustomers();
    }

    savePurchase() {
        const form = document.getElementById('purchase-form');
        const formData = new FormData(form);
        const purchaseData = Object.fromEntries(formData.entries());
        
        // Get product name
        const productSelect = document.getElementById('purchase-product');
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        purchaseData.productName = selectedOption.dataset.name;
        
        // Convert numeric fields
        purchaseData.quantity = parseInt(purchaseData.quantity);
        purchaseData.unitCost = parseFloat(purchaseData.unitCost);
        purchaseData.totalAmount = parseFloat(purchaseData.totalAmount);

        dataManager.addPurchase(purchaseData);

        this.closeModal();
        app.loadPurchases();
        app.loadInventory();
        app.updateNotificationCount();
    }

    saveSale() {
        const form = document.getElementById('sale-form');
        const formData = new FormData(form);
        const saleData = Object.fromEntries(formData.entries());
        
        // Get customer and product names
        const customerSelect = document.getElementById('sale-customer');
        const productSelect = document.getElementById('sale-product');
        const customerOption = customerSelect.options[customerSelect.selectedIndex];
        const productOption = productSelect.options[productSelect.selectedIndex];
        
        saleData.customerName = customerOption.dataset.name;
        saleData.productName = productOption.dataset.name;
        
        // Convert numeric fields
        saleData.quantity = parseInt(saleData.quantity);
        saleData.unitPrice = parseFloat(saleData.unitPrice);
        saleData.totalAmount = parseFloat(saleData.totalAmount);

        // Check stock availability
        const availableStock = parseInt(productOption.dataset.stock);
        if (saleData.quantity > availableStock) {
            alert(`Insufficient stock! Available: ${availableStock} units`);
            return;
        }

        dataManager.addSale(saleData);

        this.closeModal();
        app.loadSales();
        app.loadInventory();
        app.updateNotificationCount();
    }

    // Helper methods for form interactions
    updatePurchaseProductInfo() {
        const productSelect = document.getElementById('purchase-product');
        const vendorSelect = document.getElementById('purchase-vendor');
        const costInput = document.getElementById('purchase-cost');
        
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        if (selectedOption.value) {
            vendorSelect.value = selectedOption.dataset.supplier;
            costInput.value = selectedOption.dataset.cost;
            this.calculatePurchaseTotal();
        }
    }

    calculatePurchaseTotal() {
        const quantity = parseFloat(document.getElementById('purchase-quantity').value) || 0;
        const unitCost = parseFloat(document.getElementById('purchase-cost').value) || 0;
        const total = quantity * unitCost;
        document.getElementById('purchase-total').value = total.toFixed(2);
    }

    updateSaleCustomerInfo() {
        // Could add customer-specific logic here
    }

    updateSaleProductInfo() {
        const productSelect = document.getElementById('sale-product');
        const priceInput = document.getElementById('sale-price');
        
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        if (selectedOption.value) {
            priceInput.value = selectedOption.dataset.price;
            this.calculateSaleTotal();
        }
    }

    calculateSaleTotal() {
        const quantity = parseFloat(document.getElementById('sale-quantity').value) || 0;
        const unitPrice = parseFloat(document.getElementById('sale-price').value) || 0;
        const total = quantity * unitPrice;
        document.getElementById('sale-total').value = total.toFixed(2);
    }
}

// Global modal functions
function showAddProductModal() {
    modalManager.showProductModal();
}

function showAddVendorModal() {
    modalManager.showVendorModal();
}

function showAddCustomerModal() {
    modalManager.showCustomerModal();
}

function showAddPurchaseModal() {
    modalManager.showPurchaseModal();
}

function showAddSaleModal() {
    modalManager.showSaleModal();
}

// Initialize modal manager
const modalManager = new ModalManager();

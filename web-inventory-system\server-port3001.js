// PostgreSQL Server on Port 3001
const express = require('express');
const cors = require('cors');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const app = express();
const PORT = 3001; // Different port

console.log('🚀 Starting PostgreSQL server on port 3001...');

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// PostgreSQL connection
const pool = new Pool({
    user: process.env.DB_USER || 'postgres',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'inventory_management',
    password: process.env.DB_PASSWORD || 'root',
    port: process.env.DB_PORT || 5432,
});

// Health check
app.get('/api/health', async (req, res) => {
    try {
        const result = await pool.query('SELECT NOW() as time, COUNT(*) as products FROM products');
        res.json({ 
            status: 'ok', 
            timestamp: result.rows[0].time,
            productCount: result.rows[0].products,
            database: 'connected'
        });
    } catch (error) {
        res.status(500).json({ 
            status: 'error', 
            error: error.message,
            database: 'disconnected'
        });
    }
});

// Products API
app.get('/api/products', async (req, res) => {
    try {
        const query = `
            SELECT 
                p.id,
                p.product_code as "hsnCode",
                p.name,
                c.name as category,
                p.cost_price as "costPrice",
                p.selling_price as "sellingPrice",
                v.name as supplier,
                p.current_stock as "currentStock"
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN vendors v ON p.vendor_id = v.id
            WHERE p.is_active = TRUE
            ORDER BY p.name
        `;
        const result = await pool.query(query);
        res.json({ success: true, data: result.rows });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

// Main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Start server immediately
app.listen(PORT, () => {
    console.log(`🚀 PostgreSQL Inventory System running on http://localhost:${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}`);
    console.log(`🔧 API Health: http://localhost:${PORT}/api/health`);
    console.log(`📦 Products API: http://localhost:${PORT}/api/products`);
});

console.log('Server setup complete!');

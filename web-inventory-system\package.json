{"name": "advanced-inventory-management-system", "version": "1.0.0", "description": "A comprehensive web-based inventory management system with 9 interactive screens", "main": "server.js", "scripts": {"start": "node server.js", "start:postgresql": "node server-postgresql.js", "dev": "nodemon server.js", "dev:postgresql": "nodemon server-postgresql.js", "install-deps": "npm install", "setup": "npm install && node setup.js", "setup:postgresql": "npm install && node database/setup-database.js", "db:setup": "node database/setup-database.js", "db:create-user": "node database/setup-database.js --create-user", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["inventory", "management", "system", "web", "dashboard", "analytics", "business", "stock", "sales", "purchase"], "author": "AI Assistant", "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "pg": "^8.11.3", "pg-pool": "^3.6.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/inventory-management-system.git"}, "bugs": {"url": "https://github.com/your-username/inventory-management-system/issues"}, "homepage": "https://github.com/your-username/inventory-management-system#readme"}